import {isArray} from 'lodash';

export const TemAuthMap = {
  MANAGE: 'MANAGE',
  EXECUTE: 'EXECUTE',
  NO_PERMISSION: '无权限'
};

// 权限检查
export const authCheck = (privileges: string[], auth: string) => {
  if (!privileges || !isArray(privileges)) {
    return true;
  }

  if (privileges.includes(TemAuthMap.MANAGE)) {
    return true;
  }

  if (auth === TemAuthMap.MANAGE) {
    return false;
  }

  return privileges.includes(TemAuthMap.EXECUTE);
};
