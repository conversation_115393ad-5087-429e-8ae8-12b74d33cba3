import {Modal, toast, Alert, Form, DatePicker} from 'acud';
import * as http from '@api/integration';
import {JobItem} from '../utils';
import {BatchOperateType, FileJobRunType} from '@api/integration/type';
import moment from 'moment';
import locale from 'acud/es/date-picker/locale/zh_CN';

export interface simpleJobItem {
  jobId: string;
}

export const onStart = (jobs: JobItem[] | simpleJobItem[], workspaceId: string, callback) => {
  // 如果 job 没有 name 字段，则使用 '任务' 作为默认名称（适用于在 Create/Edit 页面创建任务后直接运行）
  const jobNames = jobs.map((job) => `"${job.name || '任务'}"`).join('、');
  const isBatch = jobs.length > 1;
  let scheduleTime = moment();
  const onScheduleTimeChange = (value: moment.Moment) => {
    scheduleTime = value;
  };
  Modal.confirm({
    width: 620,
    title: `${jobs.length === 1 ? '' : '批量'}运行`,
    okText: '运行',
    content: (
      <>
        <Alert
          message="您可在「任务列表-最近运行」查看正在执行的任务，通过任务名称查看该任务的详细运行记录"
          type="info"
        />
        <Form className="mt-4">
          <Form.Item
            label="业务时间"
            name="scheduleTime"
            extra="设置的业务时间仅在任务配置中明确包含变量${logicTime}时才生效"
          >
            <DatePicker
              showTime
              showNow={true}
              onChange={onScheduleTimeChange}
              defaultValue={scheduleTime}
              locale={locale}
            />
          </Form.Item>
        </Form>
      </>
    ),
    onOk: async () => {
      try {
        const res = isBatch
          ? await http.batchOperationIntegrationJob(workspaceId, BatchOperateType.Start, {
              jobIds: jobs.map((item) => item.jobId),
              scheduleTime: scheduleTime.format('YYYY-MM-DD HH:mm:ss'),
              triggerType: FileJobRunType.Once
            })
          : await http.startIntegrationJob(workspaceId, jobs[0].jobId, {
              runtimeArgs: {
                scheduleTime: scheduleTime.format('YYYY-MM-DD HH:mm:ss'),
                triggerType: FileJobRunType.Once
              },
              isPublished: true
            });
        if (res.success) {
          toast.success({message: `${jobNames}运行成功`, duration: 5});
          callback();
        }
      } catch {
        console.error('运行任务失败');
      }
    }
  });
};
