/**
 * 常驻实例新建页
 * @author: <EMAIL>
 */
import React, {useContext, useState, useEffect, useMemo} from 'react';
import {Form, Input, Select, toast, Radio} from 'acud';

import {useNavigate} from 'react-router-dom';
import {createComputeResource} from '@api/Compute';
import {checkDorisNodeCountWhiteList} from '@/api/auth';

import urls from '@utils/urls';
import IconSvg from '@components/IconSvg';
import {WorkspaceContext} from '@pages/index';
import OrderDetail from '@pages/Compute/components/OrderDetail';
import {NAME_LIMIT_LENGTH, NAME_ERROR_MESSAGE, NAME_REGEX, PAY_TYPE} from '../../config';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import SparkConfig from '@pages/Compute/components/SparkConfig';
import useNetworkWithClusterType from '../../hooks/useNetworkWithClusterType';
import usePayAndRegion from '../../hooks/usePayAndRegion';
import useEnv from '@hooks/useEnv';
const cx = classNames.bind(styles);

const rayVersionOptions = [{label: '1.0', value: '1.0'}];
const sparkVersionOptions = [{label: '3.5.5', value: '3.5.5'}];

const BasedInstanceCreate: React.FC = () => {
  const {isQasandbox, isPrivate} = useEnv();
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const {workspaceId} = useContext(WorkspaceContext);
  const [mirrorVersionOptions, setMirrorVersionOptions] = useState(rayVersionOptions);
  const [isInWhitelist, setIsInWhitelist] = useState(false);

  const chargingType = Form.useWatch('chargingType', form);
  const nodeCnt = Form.useWatch('nodeCnt', form);
  const clusterType = Form.useWatch('clusterType', form);
  const engine = Form.useWatch('engine', form);
  const mirrorVersion = Form.useWatch('mirrorVersion', form);
  const orderTime = Form.useWatch('orderTime', form);

  const {currentRegion, renderPayTypeAndRegion, renderPayConfig} = usePayAndRegion();

  const initialValues = {
    workspaceId,
    chargingType: 'Postpaid',
    region: currentRegion?.id,
    nodeType: 'CPU',
    clusterType: '',
    nodeCnt: 1,
    engine: 'Ray',
    mirrorVersion: '1.0',
    orderTime: '1-MONTH'
  };

  const nameRules = [
    {required: true, message: '请输入名称'},
    {
      validator: (_, value) => {
        const rule = NAME_REGEX;
        if (!rule.test(value)) {
          return Promise.reject(new Error(NAME_ERROR_MESSAGE));
        }
        return Promise.resolve();
      }
    }
  ];

  function goComputeList() {
    navigate(`${urls.compute}?workspaceId=${workspaceId}&tab=dataProcess`);
  }

  const onEngineChange = (e) => {
    form.setFieldsValue({
      mirrorVersion: e.target.value === 'Ray' ? '1.0' : '3.5.5',
      nodeCnt: e.target.value === 'Ray' ? 1 : 2,
      nodeType: 'CPU',
      clusterType: ''
    });
    setMirrorVersionOptions(e.target.value === 'Ray' ? rayVersionOptions : sparkVersionOptions);
  };

  // 判断是否在节点数量白名单内
  const getIsInWhitelist = async () => {
    const whiteListCheck = await checkDorisNodeCountWhiteList();
    const isInWhitelist = whiteListCheck?.result?.inWhitelist || false;
    setIsInWhitelist(isInWhitelist);
  };

  // 调用白名单接口
  useEffect(() => {
    getIsInWhitelist();
  }, []);

  // input 参数限制，白名单用户没有最大数量限制
  const nodeCountInputParams = useMemo(() => {
    return {
      min: engine === 'Ray' ? 1 : 2,
      ...(isInWhitelist ? {} : {max: 5})
    };
  }, [isInWhitelist, engine]);

  // 网络和可用区，节点规格相关逻辑
  const {zoneName, renderNetWorkAndZone, renderClusterConfig} = useNetworkWithClusterType({
    engine,
    form,
    nodeCountInputParams,
    nodeTypeOptions:
      engine === 'Spark'
        ? [{value: 'CPU', label: 'CPU'}]
        : [
            {value: 'CPU', label: 'CPU'},
            {value: 'GPU', label: 'GPU'}
          ]
  });

  const detailItems = useMemo(() => {
    return [
      isPrivate ? null : {label: '付费方式', value: PAY_TYPE.getTextFromValue(chargingType)},
      isPrivate ? null : {label: '地域', value: currentRegion?.label},
      {label: '实例类型', value: engine},
      {label: '镜像版本', value: mirrorVersion},
      isPrivate ? null : {label: '可用区', value: zoneName},
      {label: '节点规格', value: clusterType},
      {label: '节点总数', value: nodeCnt}
    ].filter(Boolean);
  }, [currentRegion, mirrorVersion, zoneName, clusterType, nodeCnt, chargingType, engine, isPrivate]);

  const handleSubmit = async () => {
    await form.validateFields();
    const values = await form.getFieldsValue();
    const res = await createComputeResource({
      ...values,
      workspaceId
    });
    if (res.success) {
      // todo: 需要去掉
      if (!isQasandbox) {
        toast.success({
          message: '创建成功',
          duration: 3
        });
        goComputeList();
      }
      return res;
    }
  };

  // 询价参数
  const priceParams = useMemo(() => {
    return {
      chargingType,
      region: currentRegion?.id,
      clusterType,
      nodeCnt,
      orderTime,
      subServiceType: 'general compute',
      workspaceId,
      tab: 'dataProcess',
      detailTitle: `${currentRegion?.label} ${zoneName}`,
      configs: [
        {label: '节点规格', value: clusterType},
        {label: '节点总数', value: nodeCnt},
        {label: '镜像版本', value: `${engine} ${mirrorVersion}`}
      ],
      chargeItem: '数据处理计算实例'
    };
  }, [
    chargingType,
    currentRegion,
    clusterType,
    nodeCnt,
    orderTime,
    workspaceId,
    mirrorVersion,
    engine,
    zoneName
  ]);

  return (
    <main className={cx('compute-wrapper')}>
      <div className={cx('compute-content')}>
        <div className={cx('compute-title')}>
          <IconSvg
            type="right"
            size={18}
            color="#5C5F66"
            className={cx('compute-title-icon')}
            onClick={() => {
              goComputeList();
            }}
          />
          创建常驻实例
        </div>
        <div className={cx('form-wrapper')}>
          <Form
            form={form}
            initialValues={initialValues}
            labelAlign="left"
            inputMaxWidth="480px"
            labelWidth="94px"
            colon={false}
          >
            {/* 付费及地域 ， 私有化部署「无」 */}
            {renderPayTypeAndRegion()}
            {/* 网络和可用区， 私有化部署「无」*/}
            {renderNetWorkAndZone()}
            <div className={styles['legend-title']}>实例信息</div>
            <Form.Item name="name" extra={NAME_ERROR_MESSAGE} label="实例名称" rules={nameRules}>
              <Input limitLength={NAME_LIMIT_LENGTH} forbidIfLimit></Input>
            </Form.Item>
            <Form.Item name="engine" label="实例类型">
              <Radio.Group
                options={[
                  {value: 'Ray', label: 'Ray'},
                  {value: 'Spark', label: 'Spark'}
                ]}
                onChange={onEngineChange}
                optionType="button"
              ></Radio.Group>
            </Form.Item>
            <Form.Item className={cx('margin-bottom-40px')} name="mirrorVersion" label="镜像版本" required>
              <Select options={mirrorVersionOptions} className="w-full"></Select>
            </Form.Item>
            {engine === 'Spark' ? (
              <Form.Item name="sparkConfig" label="Spark配置">
                <SparkConfig></SparkConfig>
              </Form.Item>
            ) : null}
            <div className={styles['legend-title']}>节点配置</div>
            {renderClusterConfig()}
            {/* 支付设置 */}
            {renderPayConfig(chargingType)}
          </Form>
        </div>

        <OrderDetail items={detailItems} priceParams={priceParams} showPrice={true} onSubmit={handleSubmit} />
      </div>
    </main>
  );
};
export default BasedInstanceCreate;
