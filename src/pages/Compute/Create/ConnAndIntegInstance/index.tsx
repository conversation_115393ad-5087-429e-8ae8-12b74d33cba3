/**
 * 创建源连接与集成实例
 * <AUTHOR>
 */
import React, {useState, useContext, useMemo} from 'react';
import {Form, Input, toast} from 'acud';
import {createComputeResource, Engine} from '@api/Compute';
import {WorkspaceContext} from '@pages/index';
import {NAME_LIMIT_LENGTH, PAY_TYPE, ComputedMap} from '../../config';
import {useNavigate} from 'react-router-dom';
import urls from '@utils/urls';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import IconSvg from '@components/IconSvg';
import OrderDetail from '@pages/Compute/components/OrderDetail';
import useNetworkWithClusterType from '../../hooks/useNetworkWithClusterType';
import usePayAndRegion from '../../hooks/usePayAndRegion';
import useEnv from '@hooks/useEnv';

const cx = classNames.bind(styles);

const ConnAndIntegCreate: React.FC = () => {
  const {isQasandbox, isPrivate} = useEnv();
  const [form] = Form.useForm();
  const {workspaceId} = useContext(WorkspaceContext);
  // const [isInWhitelist, setIsInWhitelist] = useState(false);
  const [loading, setLoading] = useState(false);

  const chargingType = Form.useWatch('chargingType', form);
  const nodeCnt = Form.useWatch('nodeCnt', form);
  const clusterType = Form.useWatch('clusterType', form);
  const orderTime = Form.useWatch('orderTime', form);

  const navigate = useNavigate();
  const {currentRegion, renderPayTypeAndRegion, renderPayConfig} = usePayAndRegion();

  const initialValues = {
    workspaceId,
    chargingType: 'Postpaid',
    region: currentRegion?.id,
    nodeType: 'CPU',
    clusterType: '',
    nodeCnt: 2,
    orderTime: '1-MONTH'
  };

  const ruleText = ComputedMap[Engine.Seatunnel].ruleText;

  const nameRules = [
    {required: true, message: '请输入名称'},
    {
      validator: (_, value) => {
        const rule = ComputedMap[Engine.Seatunnel].rule;
        if (!rule.test(value)) {
          return Promise.reject(new Error(ruleText));
        }
        return Promise.resolve();
      }
    }
  ];

  const handleSubmit = async () => {
    await form.validateFields();
    const values = await form.getFieldsValue();
    const res = await createComputeResource({
      ...values,
      engine: Engine.Seatunnel,
      workspaceId
    });
    if (res.success) {
      // todo: 需要去掉
      if (!isQasandbox) {
        toast.success({
          message: '创建成功',
          duration: 3
        });
        goComputeList();
      }
      return res;
    }
  };

  function goComputeList() {
    navigate(`${urls.compute}?workspaceId=${workspaceId}&tab=dataIntegration`);
  }

  // 判断是否在节点数量白名单内
  // const getIsInWhitelist = async () => {
  //   const whiteListCheck = await checkDorisNodeCountWhiteList();
  //   const isInWhitelist = whiteListCheck?.result?.inWhitelist || false;
  //   setIsInWhitelist(isInWhitelist);
  // };

  // 调用白名单接口
  // useEffect(() => {
  //   getIsInWhitelist();
  // }, []);

  const nodeCountInputParams = useMemo(() => {
    return {
      min: 2,
      max: 5
    };
  }, []);

  // 网络和可用区，节点规格相关逻辑
  const {zoneName, renderNetWorkAndZone, renderClusterConfig} = useNetworkWithClusterType({
    engine: 'Seatunnel',
    form,
    nodeCountInputParams,
    nodeTypeOptions: [{value: 'CPU', label: 'CPU'}]
  });

  const detailItems = useMemo(() => {
    return [
      isPrivate ? null : {label: '付费方式', value: PAY_TYPE.getTextFromValue(chargingType)},
      isPrivate ? null : {label: '地域', value: currentRegion?.label},
      isPrivate ? null : {label: '可用区', value: zoneName},
      {label: '节点规格', value: clusterType},
      {label: '节点总数', value: nodeCnt}
    ].filter(Boolean);
  }, [currentRegion, zoneName, clusterType, nodeCnt, chargingType, isPrivate]);

  // 询价参数
  const priceParams = useMemo(() => {
    return {
      chargingType,
      region: currentRegion?.id,
      clusterType,
      nodeCnt,
      orderTime,
      subServiceType: 'general compute',
      workspaceId,
      tab: 'dataIntegration',
      detailTitle: `${currentRegion?.label} ${zoneName}`,
      configs: [
        {label: '节点规格', value: clusterType},
        {label: '节点总数', value: nodeCnt}
      ],
      chargeItem: '源连接与集成计算实例'
    };
  }, [chargingType, currentRegion, clusterType, nodeCnt, orderTime, workspaceId, zoneName]);

  return (
    <main className={cx('compute-wrapper')}>
      <div className={cx('compute-content')}>
        <div className={cx('compute-title', 'flex', 'items-center')}>
          <IconSvg
            type="right"
            size={18}
            color="#5C5F66"
            className={cx('compute-title-icon')}
            onClick={() => {
              goComputeList();
            }}
          />
          创建{ComputedMap[Engine.Seatunnel].text}
        </div>
        <div className={cx('form-wrapper')}>
          <Form
            form={form}
            initialValues={initialValues}
            labelAlign="left"
            inputMaxWidth="480px"
            labelWidth="94px"
            colon={false}
          >
            {/*付费及地域， 私有化部署「无」*/}
            {renderPayTypeAndRegion()}
            {/* 网络和可用区， 私有化部署「无」 */}
            {renderNetWorkAndZone()}
            <div className={styles['legend-title']}>实例信息</div>
            <Form.Item name="name" extra={ruleText} label="实例名称" rules={nameRules}>
              <Input limitLength={NAME_LIMIT_LENGTH} forbidIfLimit></Input>
            </Form.Item>
            <div className={styles['legend-title']}>节点配置</div>
            {renderClusterConfig()}
            {/* 支付设置 */}
            {renderPayConfig(chargingType)}
          </Form>
        </div>
        <OrderDetail items={detailItems} showPrice={true} priceParams={priceParams} onSubmit={handleSubmit} />
      </div>
    </main>
  );
};

export default ConnAndIntegCreate;
