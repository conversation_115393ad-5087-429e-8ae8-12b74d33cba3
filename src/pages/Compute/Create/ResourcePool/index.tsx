/**
 * 常驻实例新建页
 * @author: <EMAIL>
 */
import React, {useContext, useMemo} from 'react';
import _ from 'lodash';
import {Form, Input, toast} from 'acud';
import {useNavigate} from 'react-router-dom';
import {CreateComputeResourcePoolParams, Engine, createComputeResourcePool} from '@api/Compute';
import urls from '@utils/urls';
import IconSvg from '@components/IconSvg';
import {WorkspaceContext} from '@pages/index';
import OrderDetail from '@pages/Compute/components/OrderDetail';
import ImageVersionSelect from '@pages/Compute/components/ImageVersionSelect';
import {NAME_LIMIT_LENGTH, NAME_ERROR_MESSAGE, NAME_REGEX, PAY_TYPE} from '../../config';
import useNetworkWithClusterType from '../../hooks/useNetworkWithClusterType';
import usePayAndRegion from '../../hooks/usePayAndRegion';
import useEnv from '@hooks/useEnv';

import styles from './index.module.less';
import classNames from 'classnames/bind';

const cx = classNames.bind(styles);

const BasedInstanceCreate: React.FC = () => {
  const {isPrivate, isQasandbox} = useEnv();
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const {workspaceId} = useContext(WorkspaceContext);

  const chargingType = Form.useWatch('chargingType', form);
  const nodeCnt = Form.useWatch('nodeCnt', form);
  const clusterType = Form.useWatch('clusterType', form);
  const preInstalledImage = Form.useWatch('preInstalledImage', form);
  const orderTime = Form.useWatch('orderTime', form);

  const {currentRegion, renderPayTypeAndRegion, renderPayConfig} = usePayAndRegion();

  const initialValues = {
    workspaceId,
    chargingType: 'Postpaid',
    region: currentRegion?.id,
    nodeType: 'CPU',
    clusterType: '',
    nodeCnt: 1,
    orderTime: '1-MONTH'
  };
  const nameRules = [
    {required: true, message: '请输入名称'},
    {
      validator: (_, value) => {
        const rule = NAME_REGEX;
        if (!rule.test(value)) {
          return Promise.reject(new Error(NAME_ERROR_MESSAGE));
        }
        return Promise.resolve();
      }
    }
  ];

  function goComputeList() {
    navigate(`${urls.compute}?workspaceId=${workspaceId}&tab=resourcePool`);
  }

  // 网络和可用区，节点规格相关逻辑
  const {zoneName, renderNetWorkAndZone, renderNodeType, renderClusterType, renderNodeCount} =
    useNetworkWithClusterType({
      engine: 'ResourcePool',
      form,
      nodeCountInputParams: {min: 1, max: 10}
    });

  const detailItems = useMemo(() => {
    return [
      isPrivate ? null : {label: '付费方式', value: PAY_TYPE.getTextFromValue(chargingType)},
      isPrivate ? null : {label: '地域', value: currentRegion?.label},
      isPrivate ? null : {label: '可用区', value: zoneName},
      {label: '资源规格', value: clusterType},
      {label: '资源数量', value: nodeCnt},
      {label: '镜像版本', value: preInstalledImage}
    ].filter(Boolean);
  }, [isPrivate, currentRegion, zoneName, clusterType, nodeCnt, chargingType, preInstalledImage]);

  const handleSubmit = async () => {
    await form.validateFields();
    const values = await form.getFieldsValue();
    const preInstalledImage = values.preInstalledImage;
    let imageParam = {};
    if (preInstalledImage) {
      const [engine, mirrorVersion] = preInstalledImage.split(' ');
      imageParam = {
        engine,
        mirrorVersion
      };
    }

    const res = await createComputeResourcePool(
      _.omit(
        {
          ...values,
          ...imageParam,
          workspaceId
        },
        ['preInstalledImage']
      ) as CreateComputeResourcePoolParams
    );

    if (res.success) {
      // todo: 需要去掉
      if (!isQasandbox) {
        toast.success({
          message: '创建成功',
          duration: 3
        });
        goComputeList();
      }
      return res;
    }
  };

  // 询价参数
  const priceParams = useMemo(() => {
    return {
      chargingType,
      region: currentRegion?.id,
      clusterType,
      nodeCnt,
      orderTime,
      subServiceType: 'pool',
      workspaceId,
      tab: 'resourcePool',
      detailTitle: `${currentRegion?.label} ${zoneName}`,
      configs: [
        {label: '资源规格', value: clusterType},
        {label: '资源数量', value: nodeCnt}
      ],
      chargeItem: '资源池'
    };
  }, [chargingType, currentRegion, clusterType, nodeCnt, orderTime, workspaceId, zoneName]);
  return (
    <main className={cx('compute-wrapper')}>
      <div className={cx('compute-content')}>
        <div className={cx('compute-title')}>
          <IconSvg
            type="right"
            size={18}
            color="#5C5F66"
            className={cx('compute-title-icon')}
            onClick={() => {
              goComputeList();
            }}
          />
          创建资源池
        </div>
        <div className={cx('form-wrapper')}>
          <Form
            form={form}
            initialValues={initialValues}
            labelAlign="left"
            inputMaxWidth="480px"
            labelWidth="94px"
            colon={false}
          >
            {/* 付费及地域， 私有化部署「无」 */}
            {renderPayTypeAndRegion()}
            {/* 网络和可用区， 私有化部署「无」 */}
            {renderNetWorkAndZone()}
            <div className={styles['legend-title']}>资源池信息</div>
            <Form.Item name="name" extra={NAME_ERROR_MESSAGE} label="资源池名称" rules={nameRules}>
              <Input limitLength={NAME_LIMIT_LENGTH} forbidIfLimit></Input>
            </Form.Item>
            {renderNodeType({
              label: '资源类型',
              nodeTypeOptions: [{value: 'CPU', label: 'CPU'}]
            })}
            {renderClusterType({label: '资源规格'})}
            {renderNodeCount({label: '资源数量'})}
            <Form.Item name="preInstalledImage" label="镜像版本（预加载）">
              <ImageVersionSelect engines={[Engine.Spark]}></ImageVersionSelect>
            </Form.Item>
            {/* 支付设置 */}
            {renderPayConfig(chargingType)}
          </Form>
        </div>

        <OrderDetail items={detailItems} showPrice={true} priceParams={priceParams} onSubmit={handleSubmit} />
      </div>
    </main>
  );
};
export default BasedInstanceCreate;
