/**
 * 数据处理实例 - 新建任务实例
 *
 * <AUTHOR>
 */

import React, {useContext, useState, useMemo, useEffect} from 'react';
import _ from 'lodash';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import {Form, Select, Input, InputNumber, Radio, Button, toast, Loading} from 'acud';
import {useRegion} from '@hooks/useRegion';
import {useNavigate, Link} from 'react-router-dom';
import {useRequest} from 'ahooks';
import useUrlState from '@ahooksjs/use-url-state';
import useEnv from '@hooks/useEnv';
import {
  getZoneList,
  Zone,
  createTaskInstanceTemplate,
  getTaskInstanceTemplateDetail,
  updateTaskInstanceTemplate,
  getComputeResourcePoolList
} from '@api/Compute';
import {checkDorisNodeCountWhiteList} from '@/api/auth';
import urls from '@utils/urls';
import {WorkspaceContext} from '@pages/index';
import OrderDetail from '@pages/Compute/components/OrderDetail';
import {PAY_TYPE, NAME_LIMIT_LENGTH, NAME_REGEX, NAME_ERROR_MESSAGE} from '@pages/Compute/config';
import IconSvg from '@components/IconSvg';
import PaySelect from '@pages/Compute/components/PaySelect';
import SubnetSelect from '@pages/Compute/components/SubnetSelect';
import VpcSelect from '@pages/Compute/components/VpcSelect';
import TextEllipsis from '@components/TextEllipsisTooltip';

const cx = classNames.bind(styles);
const payTypeOptions = PAY_TYPE.toArray('POSTPAID', 'PREPAID').map((item) => ({
  ...item,
  value: item.value,
  label: item.text,
  disabled: item.value === PAY_TYPE.PREPAID
}));
const instanceTypeOptions = [
  {label: 'Spark', value: 'Spark'}
  // {label: 'Ray', value: 'Ray'}
];
// 本期不做
// const waitingStrategyOptions = [
//   {
//     value: 'handleWait',
//     label: '等待资源池资源充足后自动创建',
//     tip: '任务实例创建等待，会导致任务实例绑定的工作流任务状态变为等待资源，对应工作流的状态为运行中'
//   },
//   {
//     value: 'handleFail',
//     label: '不自动创建',
//     tip: '任务实例创建失败，对应工作流的状态为失败'
//   }
// ];
const mirrorVersionOptionsMap = {
  Spark: [{label: '3.5.5', value: '3.5.5'}],
  Ray: [{label: '1.0', value: '1.0'}]
};
export interface ZoneOption {
  value: string;
  label: string;
  disabled?: boolean;
  specs: Zone['specs'];
}
const TaskInstanceCreate: React.FC = () => {
  const [form] = Form.useForm();
  const {currentRegion} = useRegion();
  const navigate = useNavigate();
  const {workspaceId} = useContext(WorkspaceContext);
  const [zoneList, setZoneList] = useState<ZoneOption[]>([]);
  const [engine, setEngine] = useState('Spark');
  const [mirrorVersionOptions, setMirrorVersionOptions] = useState<{label: string; value: string}[]>(
    mirrorVersionOptionsMap.Spark
  );
  const [nodeType, setNodeType] = useState('CPU');
  const [nodeCnt, setNodeCnt] = useState(1);
  // 公有云 - 节点规格 - 资源池列表
  const [poolOptions, setPoolOptions] = useState([]);
  // 私有化 - 节点规格
  const [clusterTypeOptions, setClusterTypeOptions] = useState([]);
  const [clusterType, setClusterType] = useState('');
  const [resourcePoolName, setResourcePoolName] = useState('');
  const [isInWhitelist, setIsInWhitelist] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const vpcId = Form.useWatch('vpcId', form);
  const availableZone = Form.useWatch('availableZone', form);
  const resourcePoolId = Form.useWatch('resourcePoolId', form);
  const [urlState, setUrlState] = useUrlState({isEdit: false, templateId: ''});
  // 编辑态初始化是否完成
  const [editInited, setEditInited] = useState(false);
  // 选中子网的shortId
  const [activeShortId, setActiveShortId] = useState<string | undefined>(undefined);
  const {isPrivate} = useEnv();
  const initialValues = {
    workspaceId,
    // chargeType: 'Postpaid',
    region: currentRegion?.id,
    nodeType: 'CPU',
    nodeCnt: 1,
    engine: 'Spark',
    mirrorVersion: '3.5.5'
  };
  // 字段规则定义
  const nameRules = [
    {required: true, message: '请输入名称'},
    {
      validator: (_, value) => {
        const rule = NAME_REGEX;
        if (!rule.test(value)) {
          return Promise.reject(new Error(NAME_ERROR_MESSAGE));
        }
        return Promise.resolve();
      }
    }
  ];
  const vpcRules = [{required: true, message: '请选择网络'}];
  const zoneRules = [{required: true, message: '请选择可用区'}];
  const subnetRules = [{required: true, message: '请选择子网'}];
  const clusterTypeRules = [{required: true, message: '请选择节点规格'}];
  const onVpcChange = (vpcId?: string) => {};
  const onZoneChange = (value: string) => {
    if (isPrivate) {
      const zone = zoneList.find((item) => item.value === value);
      // 节点规格
      if (zone) {
        const types = _.map(zone.specs, (item) => {
          const disabled = item.inventoryQuantity === 0;
          return {
            value: item.dataBuilderClusterType,
            label: `${item.dataBuilderClusterType} ${disabled ? '(已售罄)' : ''}`,
            type: item.type,
            disabled
          };
        });
        setClusterTypeOptions(types);
      }
    }
  };

  // 拉取可用区列表
  const {loading: zoneLoading, run: getZoneListRun} = useRequest(getZoneList, {
    manual: true,
    onSuccess: (res) => {
      if (!res.success) {
        return;
      }
      const zoneList = _.map<Zone, ZoneOption>(res.result.zones, (zone) => {
        const zoneName = zone.zoneName?.replace?.(/zone([A-Z])/g, (_, c) => `可用区${c}`);
        return {
          specs: zone.specs,
          value: zone.zoneName,
          label: zoneName
        };
      });
      setZoneList(zoneList);
    }
  });
  // 初始化可用区默认取值
  useEffect(() => {
    const validZone = zoneList[0];
    // 创建态初始化可用区取值
    if (validZone && !urlState.isEdit) {
      form.setFieldsValue({
        availableZone: validZone.value
      });
      onZoneChange(validZone.value);
    }
  }, [zoneList, form]);
  useEffect(() => {
    getZoneListRun({engine: 'JobInstance'});
  }, [getZoneListRun]);

  // 判断是否在节点数量白名单内
  const getIsInWhitelist = async () => {
    const whiteListCheck = await checkDorisNodeCountWhiteList();
    const isInWhitelist = whiteListCheck?.result?.inWhitelist || false;
    setIsInWhitelist(isInWhitelist);
  };
  // 调用白名单接口
  useEffect(() => {
    getIsInWhitelist();
  }, []);

  // 编辑状态下拉取实例模板详情
  const {loading: detailLoading, run: getInstanceDetail} = useRequest(
    () => getTaskInstanceTemplateDetail({workspaceId, templateId: urlState.templateId}),
    {
      manual: true,
      onSuccess: ({result}) => {
        if (result) {
          form.setFieldsValue({
            name: result.name,
            // chargeType: result.chargeType,
            region: result.region,
            vpcId: result.vpcId,
            subnetId: result.subnetId,
            engine: result.engine,
            mirrorVersion: result.mirrorVersion || '3.5.5',
            nodeType: result.processor,
            nodeCnt: result.nodeCnt,
            availableZone: result.availableZone,
            resourcePoolId: result.resourcePoolId
          });
          setClusterType(result.clusterType);
          setResourcePoolName(result.resourcePoolName);
          setEditInited(true);
        }
      }
    }
  );

  useEffect(() => {
    if (urlState.isEdit && urlState.templateId) {
      getInstanceDetail();
    }
  }, [urlState.templateId, urlState.isEdit, workspaceId, getInstanceDetail]);
  // input 参数限制，白名单用户没有最大数量限制
  const nodeCountInputParams = useMemo(() => {
    return {
      min: 1,
      ...(isInWhitelist ? {} : {max: 10})
    };
  }, [isInWhitelist]);

  // 渲染节点规格下拉选项标签
  const renderPoolOptionLabel = (clusterType: string, poolName: string, poolId: string) => {
    return (
      <div className={cx('option-label')}>
        <div>{clusterType}</div>
        <TextEllipsis tooltip={poolName} width={200}>
          <Link
            to={`${urls.computePoolDetail}?workspaceId=${workspaceId}&resourcePoolId=${poolId}`}
            className={cx('option-label-tag')}
          >
            {poolName}
          </Link>
        </TextEllipsis>
      </div>
    );
  };
  // 拉取节点规格列表
  const {run: getPoolList, loading: poolLoading} = useRequest(getComputeResourcePoolList, {
    manual: true,
    onSuccess: (res) => {
      if (res.success) {
        const poolList = res.result.pools;
        setPoolOptions(
          _.map(poolList, (item) => ({
            label: renderPoolOptionLabel(item.clusterType, item.name, item.resourcePoolId),
            name: item.name,
            value: item.resourcePoolId,
            resourcePoolId: item.resourcePoolId,
            resourcePoolName: item.name,
            availableZone: item.availableZone,
            subnetId: item.subnetId,
            clusterType: item.clusterType
          }))
        );
      }
    }
  });
  useEffect(() => {
    if (!isPrivate) {
      getPoolList({workspaceId});
    }
  }, [workspaceId, getPoolList, isPrivate]);
  const onTypeChange = (value: string) => {
    setEngine(value);
    setMirrorVersionOptions(mirrorVersionOptionsMap[value]);
    form.setFieldsValue({
      mirrorVersion: mirrorVersionOptionsMap[value][0].value
    });
  };
  const onNodeTypeChange = (e) => {
    setNodeType(e.target.value);
  };
  const onSubnetChange = (value, activeShortId) => {
    setActiveShortId(activeShortId);
  };
  const onPoolChange = (value: string, option) => {
    setClusterType(option.clusterType);
    setResourcePoolName(option.resourcePoolName);
  };
  const onNodeCntChange = (value: number) => {
    setNodeCnt(value);
  };

  //  根据可用区、私有网络、子网过滤资源池列表
  const filteredPoolOptions = useMemo(() => {
    const {availableZone} = form.getFieldsValue();
    return poolOptions.filter((option) => {
      return option.availableZone === availableZone && option.subnetId === activeShortId;
    });
  }, [poolOptions, form, activeShortId]);
  // 供私有化使用
  const filteredClusterTypeOptions = useMemo(() => {
    return clusterTypeOptions.filter((option) => option.type === nodeType);
  }, [clusterTypeOptions, nodeType]);

  useEffect(() => {
    if (urlState.isEdit && !editInited) {
      return;
    }
    if (
      !poolLoading &&
      resourcePoolId &&
      !_.some(filteredPoolOptions, (option) => option.value === resourcePoolId)
    ) {
      form.setFieldsValue({
        resourcePoolId: ''
      });
    }
  }, [activeShortId]);

  // 订单详情信息准备
  const formValues = form.getFieldsValue();
  const zoneName = useMemo(
    () => _.find(zoneList, (item) => item.value === availableZone)?.label || '',
    [zoneList, availableZone]
  );

  const detailItems = useMemo(() => {
    return [
      // isPrivate ? null : {label: '付费方式', value: PAY_TYPE.getTextFromValue(formValues.chargeType)},
      isPrivate ? null : {label: '地域', value: currentRegion?.label},
      {label: '实例类型', value: engine},
      {label: '镜像版本', value: formValues.mirrorVersion},
      isPrivate ? null : {label: '可用区', value: zoneName},
      {label: '节点规格', value: clusterType},
      {label: '节点总数', value: formValues.nodeCnt}
    ].filter(Boolean);
  }, [
    currentRegion?.label,
    formValues.nodeCnt,
    clusterType,
    zoneName,
    // formValues.chargeType,
    formValues.mirrorVersion,
    engine,
    isPrivate
  ]);
  const handleSubmit = async () => {
    if (submitLoading) {
      return;
    }
    try {
      setSubmitLoading(true);
      await form.validateFields();
      const values = await form.getFieldsValue();
      const res = await (urlState.isEdit ? updateTaskInstanceTemplate : createTaskInstanceTemplate)({
        ...values,
        workspaceId,
        clusterType,
        resourcePoolName,
        region: currentRegion?.id,
        ...(urlState.isEdit ? {templateId: urlState.templateId} : {})
      });
      if (res.success) {
        toast.success({
          message: '创建成功',
          duration: 3
        });
        navigate(`${urls.compute}?workspaceId=${workspaceId}&tab=dataProcess&secondTab=Template`);
      }
    } catch (err) {
      console.error('创建失败：', err);
    } finally {
      setSubmitLoading(false);
    }
  };
  const handleCancel = () => {
    navigate(`${urls.compute}?workspaceId=${workspaceId}&tab=dataProcess&secondTab=Template`);
  };
  return (
    <>
      <main className={cx('create-wrapper')}>
        {detailLoading ? (
          <Loading />
        ) : (
          <div className={cx('create-content')}>
            <div className={cx('create-title')}>
              <IconSvg
                type="right"
                size={18}
                color="#5C5F66"
                className={cx('create-title-icon')}
                onClick={() => {
                  handleCancel();
                }}
              />
              {urlState.isEdit ? '编辑' : '创建'}任务实例模板
            </div>
            <div className={cx('form-wrapper')}>
              <Form
                form={form}
                initialValues={initialValues}
                labelAlign="left"
                inputMaxWidth="480px"
                labelWidth="94px"
                colon={false}
              >
                {isPrivate ? null : (
                  <>
                    <div className={styles['legend-title']}>地域</div>
                    {/* <Form.Item name="chargeType" label="付费方式" required>
                      <PaySelect options={payTypeOptions as any[]}></PaySelect>
                    </Form.Item> */}
                    <Form.Item className={cx('margin-bottom-40px')} name="region" label="地域" required>
                      <span>{currentRegion?.label}</span>
                    </Form.Item>
                    <div className={styles['legend-title']}>网络及可用区</div>
                    <Form.Item name="vpcId" label="网络" rules={vpcRules}>
                      <VpcSelect onChange={onVpcChange} isEdit={urlState.isEdit}></VpcSelect>
                    </Form.Item>
                    <Form.Item className={cx('margin-bottom-40px')} label="可用区与子网" required>
                      <div className={cx('combine-zone-subnet', 'flex')}>
                        <Form.Item className={cx('zone-select')} name="availableZone" rules={zoneRules}>
                          <Select loading={zoneLoading} options={zoneList} onChange={onZoneChange}></Select>
                        </Form.Item>
                        <Form.Item className="flex-1" name="subnetId" rules={subnetRules}>
                          <SubnetSelect
                            vpcId={vpcId}
                            zone={availableZone}
                            onChange={onSubnetChange}
                          ></SubnetSelect>
                        </Form.Item>
                      </div>
                      <div className="acud-form-item-extra">
                        如需创建新的子网，您可以到
                        <a href="/network/#/vpc/subnet/list" target="_blank" rel="noreferrer">
                          私有网络-子网
                        </a>
                        创建
                      </div>
                    </Form.Item>
                  </>
                )}

                <div className={styles['legend-title']}>实例信息</div>
                <Form.Item name="name" extra={NAME_ERROR_MESSAGE} label="实例模板名称" rules={nameRules}>
                  <Input limitLength={NAME_LIMIT_LENGTH} forbidIfLimit></Input>
                </Form.Item>
                <Form.Item name="engine" label="实例类型" required>
                  <Select className="w-full" options={instanceTypeOptions} onChange={onTypeChange}></Select>
                </Form.Item>
                <Form.Item name="mirrorVersion" label="镜像版本" required>
                  <Select className="w-full" options={mirrorVersionOptions}></Select>
                </Form.Item>
                <div className={styles['legend-title']}>节点配置</div>
                <Form.Item name="nodeType" label="节点类型">
                  <Radio.Group
                    options={[
                      {value: 'CPU', label: 'CPU'}
                      // {value: 'GPU', label: 'GPU'}
                    ]}
                    onChange={onNodeTypeChange}
                    optionType="button"
                  ></Radio.Group>
                </Form.Item>
                {isPrivate ? (
                  <Form.Item name="clusterType" label="节点规格" rules={clusterTypeRules}>
                    <Select className="w-full" options={filteredClusterTypeOptions}></Select>
                  </Form.Item>
                ) : (
                  <Form.Item name="resourcePoolId" label="节点规格" rules={clusterTypeRules}>
                    <Select className="w-full" options={filteredPoolOptions} onChange={onPoolChange}></Select>
                  </Form.Item>
                )}

                <Form.Item name="nodeCnt" label="购买数量" required>
                  <InputNumber {...nodeCountInputParams} symmetryMode onChange={onNodeCntChange} />
                </Form.Item>
                {/* 本期不做 */}
                {/* <Form.Item
                className="flex-col"
                name="waitingStrategy"
                label="如果资源池资源不足，任务实例是否等待资源充足后继续创建"
              >
                <Radio.Group className="pl-[10px]">
                  {waitingStrategyOptions.map((option) => (
                    <React.Fragment key={option.value}>
                      <Radio className="inline-block mt-[6px] mb-[6px]" value={option.value}>
                        {option.label}
                      </Radio>
                      <div className={cx(styles['option-tip'], 'pl-[25px]')}>{option.tip}</div>
                    </React.Fragment>
                  ))}
                </Radio.Group>
              </Form.Item> */}
              </Form>
            </div>
            <div className={styles['create-footer']}>
              <Button onClick={handleSubmit} type="primary" size="large" loading={submitLoading}>
                {urlState.isEdit ? '保存' : '创建'}模版
              </Button>
              <Button className="ml-[10px]" onClick={handleCancel} size="large">
                取消
              </Button>
            </div>
            <OrderDetail items={detailItems} showPrice={false} />
          </div>
        )}
      </main>
    </>
  );
};
export default TaskInstanceCreate;
