/**
 * 查询检索实例新建页
 * @author: <EMAIL>
 */
import React, {useContext, useState, useEffect, useMemo} from 'react';
import _ from 'lodash';
import {Form, Input, toast} from 'acud';

import {useNavigate} from 'react-router-dom';

import {createComputeResource} from '@api/Compute';
import {checkDorisNodeCountWhiteList} from '@/api/auth';

import urls from '@utils/urls';
import {RULE} from '@utils/regs';

import IconSvg from '@components/IconSvg';
import {WorkspaceContext} from '@pages/index';
import OrderDetail from '@pages/Compute/components/OrderDetail';
import {NAME_LIMIT_LENGTH, PAY_TYPE} from '../../config';
import useNetworkWithClusterType from '../../hooks/useNetworkWithClusterType';
import usePayAndRegion from '../../hooks/usePayAndRegion';
import useEnv from '@hooks/useEnv';
import styles from './index.module.less';
import classNames from 'classnames/bind';
const cx = classNames.bind(styles);

const AnalysisInstanceCreate: React.FC = () => {
  const {isPrivate, isQasandbox} = useEnv();
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const {workspaceId} = useContext(WorkspaceContext);
  const [isInWhitelist, setIsInWhitelist] = useState(false);

  const chargingType = Form.useWatch('chargingType', form);
  const nodeCnt = Form.useWatch('nodeCnt', form);
  const clusterType = Form.useWatch('clusterType', form);
  const orderTime = Form.useWatch('orderTime', form);

  const {currentRegion, renderPayTypeAndRegion, renderPayConfig} = usePayAndRegion();

  const initialValues = {
    workspaceId,
    chargingType: 'Postpaid',
    region: currentRegion?.id,
    mirrorVersion: '3.0',
    nodeType: 'CPU',
    clusterType: '',
    nodeCnt: 2
  };
  const nameRules = [
    {required: true, message: '请输入名称'},
    {
      validator: (_, value) => {
        const rule = RULE.specialNameStartEn64;
        if (!rule.test(value)) {
          return Promise.reject(new Error(RULE.specialNameStartEn64Text));
        }
        return Promise.resolve();
      }
    }
  ];

  const goComputeList = () => {
    navigate(`${urls.compute}?workspaceId=${workspaceId}&tab=dataAnalysis`);
  };

  // 判断是否在节点数量白名单内
  const getIsInWhitelist = async () => {
    const whiteListCheck = await checkDorisNodeCountWhiteList();
    const isInWhitelist = whiteListCheck?.result?.inWhitelist || false;
    setIsInWhitelist(isInWhitelist);
  };

  // 调用白名单接口
  useEffect(() => {
    getIsInWhitelist();
  }, []);

  // input 参数限制，白名单用户没有最大数量限制
  const nodeCountInputParams = useMemo(() => {
    return {
      min: 2,
      ...(isInWhitelist ? {} : {max: 5})
    };
  }, [isInWhitelist]);

  // 网络和可用区，节点规格相关逻辑
  const {zoneName, renderNetWorkAndZone, renderClusterConfig} = useNetworkWithClusterType({
    engine: 'Doris',
    form,
    nodeCountInputParams,
    nodeTypeOptions: [{value: 'CPU', label: 'CPU'}]
  });

  const detailItems = useMemo(() => {
    return [
      isPrivate ? null : {label: '付费方式', value: PAY_TYPE.getTextFromValue(chargingType)},
      isPrivate ? null : {label: '地域', value: currentRegion?.label},
      {label: '版本', value: 'Doris 3.0'},
      isPrivate ? null : {label: '可用区', value: zoneName},
      {label: '节点规格', value: clusterType},
      {label: '节点总数', value: nodeCnt}
    ].filter(Boolean);
  }, [currentRegion, zoneName, clusterType, nodeCnt, chargingType, isPrivate]);

  const handleSubmit = async () => {
    await form.validateFields();
    const values = await form.getFieldsValue();
    const res = await createComputeResource({
      ...values,
      // 只传版本号
      engine: 'Doris',
      workspaceId
    });
    if (res.success) {
      // todo: 需要去掉
      if (!isQasandbox) {
        toast.success({
          message: '创建成功',
          duration: 3
        });
        goComputeList();
      }
      return res;
    }
  };

  // 询价参数
  const priceParams = useMemo(() => {
    return {
      chargingType,
      region: currentRegion?.id,
      clusterType,
      nodeCnt,
      orderTime,
      subServiceType: 'general compute',
      workspaceId,
      tab: 'dataAnalysis',
      detailTitle: `${currentRegion?.label} ${zoneName}`,
      configs: [
        {label: '节点规格', value: clusterType},
        {label: '节点总数', value: nodeCnt},
        {label: '镜像版本', value: 'Doris 3.0'}
      ],
      chargeItem: '分析与AI搜索计算实例'
    };
  }, [chargingType, currentRegion, clusterType, nodeCnt, orderTime, workspaceId, zoneName]);
  return (
    <main className={cx('compute-wrapper')}>
      <div className={cx('compute-content')}>
        <div className={cx('compute-title')}>
          <IconSvg
            type="right"
            size={18}
            color="#5C5F66"
            className={cx('compute-title-icon')}
            onClick={() => {
              goComputeList();
            }}
          />
          创建分析与AI搜索实例
        </div>
        <div className={cx('form-wrapper')}>
          <Form
            form={form}
            initialValues={initialValues}
            labelAlign="left"
            inputMaxWidth="480px"
            labelWidth="94px"
            colon={false}
          >
            {/* 付费及地域 ， 私有化部署「无」*/}
            {renderPayTypeAndRegion()}
            {/* 网络和可用区， 私有化部署 「无」 */}
            {renderNetWorkAndZone()}
            <div className={styles['legend-title']}>实例信息</div>
            <Form.Item name="name" extra={RULE.specialNameStartEn64Text} label="实例名称" rules={nameRules}>
              <Input limitLength={NAME_LIMIT_LENGTH} forbidIfLimit></Input>
            </Form.Item>
            <Form.Item className={cx('margin-bottom-40px')} name="mirrorVersion" label="实例版本" required>
              Doris 3.0
            </Form.Item>
            <div className={styles['legend-title']}>节点配置</div>
            {renderClusterConfig()}
            {/* 支付设置 */}
            {renderPayConfig(chargingType)}
          </Form>
        </div>

        <OrderDetail items={detailItems} showPrice={true} priceParams={priceParams} onSubmit={handleSubmit} />
      </div>
    </main>
  );
};
export default AnalysisInstanceCreate;
