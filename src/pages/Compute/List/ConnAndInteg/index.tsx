/**
 * 源连接与集成实例tab页
 * 主要功能：
 * 1. 展示源连接与集成实例列表，列表支持筛选、分页和刷新
 * 2. 提供新建源连接与集成实例入口，创建按钮根据用户权限确定是否展示
 * <AUTHOR>
 */
import React, {useState, useContext, useMemo} from 'react';
import {Table, Button, toast, Modal, Loading} from 'acud';
import {Plus1} from '@baidu/xicon-react-bigdata';
import {getComputeResourceList, ComputeResourceItem, deleteComputeResource, Engine} from '@api/Compute';
import {useNavigate} from 'react-router-dom';
import urls from '@utils/urls';
import {WorkspaceContext} from '@pages/index';
import RefreshButton from '@components/RefreshButton';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import {PAY_TYPE, STATUS} from '../../config';
import TextEllipsis from '@components/TextEllipsisTooltip';
import {useTableOperate} from '../../hooks/useTableOperate';
import useTable from '@hooks/useTable';
import useEnv from '@hooks/useEnv';

const cx = classNames.bind(styles);

const ConnAndIntegList: React.FC = () => {
  const {workspaceId} = useContext(WorkspaceContext);
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const navigate = useNavigate();
  const {isPrivate} = useEnv();

  const {
    loading,
    dataSource,
    tableProps,
    loadTableData: loadComputeList
  } = useTable<ComputeResourceItem>({
    getTableApi: getComputeResourceList,
    listKey: 'computes',
    refreshDeps: [workspaceId, statusFilter],
    extraParams: {
      workspaceId,
      status: statusFilter?.join(',') || '',
      engine: Engine.Seatunnel
    },
    handleTableChange: (pagination, filters, sorter, extra) => {
      if (extra?.action === 'filter') {
        setStatusFilter(filters.status as string[]);
      }
    }
  });

  const {renderOperate} = useTableOperate({
    onDelete(record: ComputeResourceItem) {
      Modal.confirm({
        title: '删除实例',
        content:
          '实例删除后不可恢复，关联任务的运行状态会受影响，关联任务需要重新指定计算实例。确定删除实例？',
        onOk: async () => {
          const res = await deleteComputeResource({
            workspaceId,
            computeId: record.computeId || ''
          });
          if (res.success) {
            toast.success({
              message: '删除成功',
              duration: 3
            });
            loadComputeList();
          }
        }
      });
    }
  });

  const columns = useMemo(() => {
    return [
      {
        title: '实例 ID',
        dataIndex: 'computeId',
        key: 'computeId',
        width: 150
      },
      {
        title: '实例名称',
        dataIndex: 'name',
        key: 'name',
        width: 300,
        render: (name) => (
          <TextEllipsis tooltip={name} width={300}>
            {name}
          </TextEllipsis>
        )
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        filterMultiple: true,
        filters: STATUS.toArray(),
        filteredValue: statusFilter,
        render: (status: string) => {
          const statusObj = STATUS.fromValue(status);
          return <span className={cx('compute-status', statusObj.className)}>{statusObj.text}</span>;
        }
      },
      {
        title: '实例规格数量',
        dataIndex: 'clusterType',
        key: 'clusterType',
        width: 120,
        render: (_, record: ComputeResourceItem) => `${record.clusterType}（${record.nodeCount} 个节点）`
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        sorter: true,
        width: 180
      },
      isPrivate
        ? null
        : {
            title: '付费方式',
            dataIndex: 'chargeType',
            key: 'chargeType',
            width: 120,
            render: (value: string) => {
              return PAY_TYPE.getTextFromValue(value);
            }
          },
      {
        title: '操作',
        key: 'operation',
        width: 150,
        render: (record: ComputeResourceItem) => {
          return renderOperate(record);
        }
      }
    ].filter(Boolean);
  }, [renderOperate, statusFilter, isPrivate]);

  const showBlankSpace = useMemo(
    () => dataSource.length === 0 && !statusFilter?.length,
    [dataSource.length, statusFilter?.length]
  );

  // 创建实例
  const onCreateClick = () => {
    navigate(`${urls.computeCreateConnAndIntegInstance}?workspaceId=${workspaceId}`);
  };

  // 渲染空白状态
  const blankSpace = useMemo(() => {
    return (
      <div className={cx('blank-space', 'h-full')}>
        <div className={cx('blank-title')}>创建源连接与集成实例</div>
        <div className={cx('blank-desc')}>创建源连接与集成实例用于运行工作流任务</div>
        <Button
          className={cx('blank-btn')}
          type="primary"
          icon={<Plus1 className="w-4 h-4" />}
          onClick={onCreateClick}
        >
          立即创建
        </Button>
        <div className={cx('blank-img')}></div>
      </div>
    );
  }, [navigate, workspaceId]);

  if (loading && dataSource.length === 0 && !statusFilter?.length) {
    return <Loading loading={loading} />;
  }

  return (
    <div className={cx('compute-wrapper')}>
      {showBlankSpace ? (
        blankSpace
      ) : (
        <>
          <div className="flex justify-end mb-4">
            <RefreshButton onClick={loadComputeList} />
            <Button
              className="ml-[8px]"
              type="primary"
              icon={<Plus1 className="w-4 h-4" />}
              onClick={onCreateClick}
            >
              创建
            </Button>
          </div>
          <Table className={cx('compute-table')} columns={columns} rowKey="computeId" {...tableProps} />
        </>
      )}
    </div>
  );
};

export default ConnAndIntegList;
