/**
 * 数据处理实例tab标签页
 * 展示「常驻实例」和「任务实例」列表，通过子tab切换
 * <AUTHOR>
 */
import React, {useMemo, useState, useContext, useRef, useEffect, useCallback} from 'react';
import _ from 'lodash';
import {Radio, Button} from 'acud';
import {Plus1} from '@baidu/xicon-react-bigdata';
import urls from '@utils/urls';
import RefreshButton from '@components/RefreshButton';
import {WorkspaceContext} from '@pages/index';
import useUrlState from '@ahooksjs/use-url-state';
import useAuth from '@hooks/useAuth';

import TaskInstanceList from './TaskInstance';
import BasedInstanceList from './BasedInstance';
import TaskTemplateList from './TaskTemplate';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import {useNavigate} from 'react-router-dom';
const cx = classNames.bind(styles);
const options = [
  {label: '常驻实例', value: 'Ray'},
  {label: '任务实例', value: 'Task'},
  {label: '任务实例模板', value: 'Template'}
];
// 组件映射关系
const componentMap = {
  Ray: BasedInstanceList,
  Task: TaskInstanceList,
  Template: TaskTemplateList
};
const DataProcessTab: React.FC = () => {
  const [value, setValue] = useState<string>('Ray');
  const ActiveRef = useRef(null);
  const {workspaceId} = useContext(WorkspaceContext);
  const [urlState, setUrlState] = useUrlState();
  const activeOption = useMemo(() => _.find(options, {value}), [value]);
  const ActiveComponent = useMemo(() => componentMap[value], [value]) as any;
  const computeReadOnly = useAuth('workspace', 'compute') !== 'readWrite';
  const navigate = useNavigate();
  // 处理创建页返回逻辑
  useEffect(() => {
    if (urlState.secondTab) {
      setValue(urlState.secondTab);
    }
  }, [urlState.secondTab]);
  const handleChange = (e) => {
    setValue(e.target.value);
  };
  const handleRefresh = () => {
    ActiveRef.current?.refresh();
  };
  const handleCreate = useCallback(() => {
    switch (value) {
      case 'Ray':
        navigate(`${urls.computeCreateBasedInstance}?workspaceId=${workspaceId}`);
        break;
      case 'Template':
        navigate(`${urls.computeCreateTaskInstance}?workspaceId=${workspaceId}`);
    }
  }, [value, workspaceId, navigate]);
  const createButton = useMemo(() => {
    const button = (
      <Button className="ml-[8px]" type="primary" icon={<Plus1 className="w-4 h-4" />} onClick={handleCreate}>
        创建
      </Button>
    );
    return button;
  }, [handleCreate]);

  const showOprBtn = useMemo(() => {
    return ['Ray', 'Template'].includes(value);
  }, [value]);

  return (
    <>
      <div className="flex justify-between">
        <Radio.Group
          className="mb-[12px]"
          value={value}
          options={options}
          optionType="button"
          onChange={handleChange}
        ></Radio.Group>
        {/* 常驻实例&实例模板列表 展示刷新和创建按钮 */}
        {showOprBtn && (
          <div className="flex justify-end mb-4">
            <RefreshButton onClick={handleRefresh} />
            {!computeReadOnly ? createButton : null}
          </div>
        )}
      </div>

      {ActiveComponent ? <ActiveComponent ref={ActiveRef} /> : null}
    </>
  );
};

export default DataProcessTab;
