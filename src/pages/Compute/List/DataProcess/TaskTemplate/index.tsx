/**
 * 任务实例模板列表
 * 展示已创建的任务实例模版，支持编辑和删除
 * @author: zhao<PERSON><PERSON><EMAIL>
 */
import React, {useState, useCallback, useContext, useEffect, useMemo, useImperativeHandle} from 'react';
import {Table, Button, Modal, Loading} from 'acud';
import {Plus1} from '@baidu/xicon-react-bigdata';
import urls from '@utils/urls';
import {useNavigate} from 'react-router-dom';
import {TaskInstanceTemplate, getTaskInstanceTemplateList, deleteTaskInstanceTemplate} from '@api/Compute';
import useAuth from '@hooks/useAuth';
import useTable from '@hooks/useTable';
import {WorkspaceContext} from '@pages/index';
import {PAY_TYPE} from '@pages/Compute/config';
import styles from '../index.module.less';
import classNames from 'classnames/bind';
const cx = classNames.bind(styles);

export interface RefreshableListRef {
  refresh: () => void;
}
const TaskTemplateList = (_, ref) => {
  const computeReadOnly = useAuth('workspace', 'compute') !== 'readWrite';
  const navigate = useNavigate();
  const {workspaceId} = useContext(WorkspaceContext);
  const columns: any = [
    {
      title: '实例模板名称',
      key: 'name',
      dataIndex: 'name',
      width: 150,
      ellipsis: {
        showTitle: false
      }
    },
    {
      title: '实例类型',
      key: 'engine',
      dataIndex: 'engine',
      width: 80
    },
    {
      title: '镜像版本',
      key: 'mirrorVersion',
      dataIndex: 'mirrorVersion',
      width: 80
    },
    {
      title: '节点规格',
      key: 'clusterType',
      dataIndex: 'clusterType',
      width: 140,
      ellipsis: {
        showTitle: false
      },
      render: (_, record) => `${record.clusterType}（${record.nodeCnt} 个节点）`
    },
    {
      title: '创建时间',
      key: 'createAt',
      dataIndex: 'createAt',
      width: 140,
      sorter: true
    },
    // {
    //   title: '付费方式',
    //   key: 'chargeType',
    //   dataIndex: 'chargeType',
    //   width: 80,
    //   render: (value: string) => {
    //     return PAY_TYPE.getTextFromValue(value);
    //   }
    // },
    ...(computeReadOnly
      ? []
      : [
          {
            title: '操作',
            key: 'operation',
            width: 80,
            render: (_, record) => {
              return (
                <>
                  <a
                    onClick={() => {
                      handleEdit(record.id);
                    }}
                  >
                    编辑
                  </a>
                  <a className="ml-4" onClick={() => handleDelete(record.id)}>
                    删除
                  </a>
                </>
              );
            }
          }
        ])
  ];
  const {
    dataSource,
    tableProps,
    loadTableData: getTaskTemplatelist,
    loading
  } = useTable<TaskInstanceTemplate>({
    getTableApi: getTaskInstanceTemplateList,
    listKey: 'templates',
    refreshDeps: [workspaceId],
    extraParams: {
      workspaceId
    }
  });
  useImperativeHandle(ref, () => ({
    refresh: getTaskTemplatelist
  }));
  const showBlankSpace = useMemo(
    () => !computeReadOnly && dataSource.length === 0,
    [computeReadOnly, dataSource.length]
  );
  const handleCreate = useCallback(() => {
    navigate(`${urls.computeCreateTaskInstance}?workspaceId=${workspaceId}`);
  }, [workspaceId, navigate]);
  const handleEdit = (id) => {
    navigate(`${urls.computeCreateTaskInstance}?workspaceId=${workspaceId}&isEdit=true&templateId=${id}`);
  };
  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '提示',
      content: '确定要删除该模板吗？',
      onOk: () => {
        try {
          deleteTaskInstanceTemplate({workspaceId, templateId: id});
          getTaskTemplatelist();
        } catch (error) {
          console.error('删除失败', error);
        }
      }
    });
  };
  // 渲染空白状态
  const blankSpace = useMemo(() => {
    const button = (
      <Button
        className={cx('blank-btn')}
        type="primary"
        icon={<Plus1 className="w-4 h-4" />}
        onClick={handleCreate}
      >
        立即创建
      </Button>
    );
    return (
      <div className={cx('blank-space', 'h-full')}>
        <div className={cx('blank-title')}>创建任务实例模版</div>
        <div className={cx('blank-desc')}>创建常驻实例用于运行工作流任务</div>
        {button}
        <div className={cx('blank-img')}></div>
      </div>
    );
  }, [handleCreate]);

  if (loading && dataSource.length === 0) {
    return <Loading loading={loading} />;
  }

  return (
    <>
      {showBlankSpace ? (
        blankSpace
      ) : (
        <>
          <Table className={cx('compute-table')} columns={columns} rowKey="computeId" {...tableProps} />
        </>
      )}
    </>
  );
};
const RefTaskTemplateList = React.forwardRef<RefreshableListRef>(TaskTemplateList);
export default RefTaskTemplateList;
