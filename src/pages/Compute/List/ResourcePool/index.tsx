/**
 * 资源池tab页
 * 主要功能：
 * 1. 展示资源池列表，列表支持筛选、分页和刷新
 * 2. 提供新建资源池入口， 创建按钮根据用户权限确定是否展示
 * <AUTHOR>
 */
import {useState, useContext, useCallback, useMemo, forwardRef, useImperativeHandle} from 'react';
import {Table, Modal, toast, Button, Link, Loading} from 'acud';
import {Plus1} from '@baidu/xicon-react-bigdata';
import urls from '@utils/urls';
import {
  deleteComputeResource,
  getComputeResourcePoolList,
  ComputeResourcePoolItem,
  getResourcePoolAssociatedComputes
} from '@api/Compute';
import {useNavigate} from 'react-router-dom';
import {WorkspaceContext} from '@pages/index';
import {STATUS, PAY_TYPE} from '@pages/Compute/config';
import RefreshButton from '@components/RefreshButton';
import useTable from '@hooks/useTable';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import {useTableOperate} from '@pages/Compute/hooks/useTableOperate';
import useEnv from '@hooks/useEnv';

const cx = classNames.bind(styles);
export interface RefreshableListRef {
  refresh: () => void;
}

export async function deleteResourcePool({
  workspaceId,
  resourcePoolId,
  cb
}: {
  workspaceId: string;
  resourcePoolId: string;
  cb: () => void;
}) {
  try {
    const res = await getResourcePoolAssociatedComputes({
      workspaceId,
      resourcePoolId
    });

    if (res.success && res.result?.instances?.length > 0) {
      const associatedComputes = res.result.instances
        .slice(0, 3)
        .map((compute) => compute.name)
        .join(', ');
      const associatedComputesCount = res.result.instances.length > 3 ? '...' : '';
      const modalContent = `资源池正在使用中，无法删除。如需删除资源池，请先清理关联的任务计算实例。关联计算实例包括：${associatedComputes}${associatedComputesCount}`;
      Modal.warning({
        title: '提示',
        content: modalContent
      });
      return;
    }
  } catch (error) {
    console.error(error);
    return;
  }

  Modal.confirm({
    title: '删除资源池',
    content: '资源池删除后不可恢复，确定删除资源池？',
    onOk: async () => {
      const res = await deleteComputeResource({
        workspaceId,
        computeId: resourcePoolId
      });
      if (res.success) {
        toast.success({
          message: '删除成功',
          duration: 3
        });
        if (cb) {
          cb();
        }
      }
    }
  });
}

const ResourcePoolList = (_, ref) => {
  const {workspaceId} = useContext(WorkspaceContext);
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const navigate = useNavigate();
  const {isPrivate} = useEnv();

  const {loading, dataSource, tableProps, loadTableData} = useTable<ComputeResourcePoolItem>({
    getTableApi: getComputeResourcePoolList,
    refreshDeps: [statusFilter, workspaceId],
    extraParams: {
      workspaceId,
      status: statusFilter?.join(',') || ''
    },
    listKey: 'pools',
    handleTableChange: (pagination, filters, sorter, extra) => {
      if (extra?.action === 'filter') {
        setStatusFilter(filters.status as string[]);
      }
    }
  });

  useImperativeHandle(ref, () => ({
    refresh: loadTableData
  }));

  const {renderOperate} = useTableOperate({
    onDelete(record: ComputeResourcePoolItem) {
      deleteResourcePool({
        workspaceId,
        resourcePoolId: record.resourcePoolId || '',
        cb: loadTableData
      });
    }
  });

  const columns = [
    {
      title: '资源池名称',
      dataIndex: 'name',
      key: 'name',
      width: 100,
      render: (text, record) => {
        return (
          <Link
            className={cx('pool-name')}
            title={text}
            onClick={() => {
              navigate(
                `${urls.computePoolDetail}?workspaceId=${workspaceId}&resourcePoolId=${record.resourcePoolId}`
              );
            }}
          >
            {text}
          </Link>
        );
      }
    },
    {
      title: '资源池ID',
      dataIndex: 'resourcePoolId',
      key: 'resourcePoolId',
      width: 150
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      filterMultiple: true,
      filters: STATUS.toArray(),
      filteredValue: statusFilter,
      render: (status: string) => {
        const statusObj = STATUS.fromValue(status);
        return <span className={cx('compute-status', statusObj.className)}>{statusObj.text}</span>;
      }
    },
    {
      title: '资源规格',
      dataIndex: 'clusterType',
      key: 'clusterType',
      width: 120
    },
    {
      title: '资源数量（个）',
      dataIndex: 'nodeCount',
      key: 'nodeCount',
      width: 120
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      sorter: true,
      width: 180
    },
    isPrivate
      ? null
      : {
          title: '付费方式',
          dataIndex: 'chargeType',
          key: 'chargeType',
          width: 120,
          render: (value: string) => {
            return PAY_TYPE.getTextFromValue(value);
          }
        },
    {
      title: '操作',
      key: 'operation',
      width: 150,
      render: (record: ComputeResourcePoolItem) => {
        return renderOperate(record);
      }
    }
  ].filter(Boolean);

  const showBlankSpace = useMemo(
    () => dataSource.length === 0 && !statusFilter?.length,
    [dataSource.length, statusFilter?.length]
  );

  // 创建资源池
  const onCreateClick = useCallback(() => {
    navigate(`${urls.computeCreatePool}?workspaceId=${workspaceId}`);
  }, [navigate, workspaceId]);

  const createButton = useMemo(() => {
    const button = (
      <Button
        className="ml-[8px]"
        type="primary"
        icon={<Plus1 className="w-4 h-4" />}
        onClick={onCreateClick}
      >
        创建
      </Button>
    );
    return button;
  }, [onCreateClick]);

  // 渲染空白状态
  const blankSpace = (
    <div className={cx('blank-space', 'h-full')}>
      <div className={cx('blank-title')}>创建资源池</div>
      <div className={cx('blank-desc')}>
        创建资源池用于创建和运行任务计算实例，提高任务计算实例的创建效率，
        <br />
        避免因资源不足导致的任务计算实例创建失败等问题
      </div>
      <Button
        className={cx('blank-btn')}
        type="primary"
        icon={<Plus1 className="w-4 h-4" />}
        onClick={onCreateClick}
      >
        立即创建
      </Button>
      <div className={cx('blank-img')}></div>
    </div>
  );

  if (loading && dataSource.length === 0 && !statusFilter?.length) {
    return <Loading loading={loading} />;
  }

  return (
    <>
      {showBlankSpace ? (
        blankSpace
      ) : (
        <>
          <div className="flex justify-end mb-4">
            <RefreshButton onClick={loadTableData} />
            {createButton}
          </div>
          <Table className={cx('compute-table')} columns={columns} rowKey="resourcePoolId" {...tableProps} />
        </>
      )}
    </>
  );
};
const RefResourcePoolList = forwardRef<RefreshableListRef>(ResourcePoolList);
export default RefResourcePoolList;
