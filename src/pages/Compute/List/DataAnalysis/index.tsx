/**
 * 分析与AI搜索实例tab页（原查询检索实例）
 * 主要功能：
 * 1. 展示分析与AI搜索实例列表，列表支持筛选、分页和刷新
 * 2. 提供新建常驻实例入口， 创建按钮根据用户权限确定是否展示
 * <AUTHOR>
 */
import {useState, useEffect, useContext, useCallback, useMemo, forwardRef, useImperativeHandle} from 'react';
import {Table, Modal, toast, Button, Link, Tooltip, Loading} from 'acud';
import {Plus1} from '@baidu/xicon-react-bigdata';
import urls from '@utils/urls';
import {getComputeResourceList, ComputeResourceItem, deleteComputeResource, Engine} from '@api/Compute';
import useAuth from '@hooks/useAuth';
import useTable from '@hooks/useTable';
import {useNavigate} from 'react-router-dom';
import {WorkspaceContext} from '@pages/index';
import {STATUS, PAY_TYPE} from '@pages/Compute/config';
import RefreshButton from '@components/RefreshButton';
import TextEllipsis from '@components/TextEllipsisTooltip';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import {useTableOperate} from '@pages/Compute/hooks/useTableOperate';
import useEnv from '@hooks/useEnv';

const cx = classNames.bind(styles);
export interface RefreshableListRef {
  refresh: () => void;
}
const AnalysisInstanceList = (_, ref) => {
  const {isPrivate} = useEnv();
  const {workspaceId} = useContext(WorkspaceContext);
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const navigate = useNavigate();

  const {
    loading,
    dataSource,
    tableProps,
    loadTableData: loadComputeList
  } = useTable<ComputeResourceItem>({
    getTableApi: getComputeResourceList,
    listKey: 'computes',
    refreshDeps: [workspaceId, statusFilter],
    extraParams: {
      workspaceId,
      status: statusFilter?.join(',') || '',
      engine: Engine.Doris
    },
    handleTableChange: (pagination, filters, sorter, extra) => {
      if (extra?.action === 'filter') {
        setStatusFilter(filters.status as string[]);
      }
    }
  });

  const [isDorisNodeCountLimit, setIsDorisNodeCountLimit] = useState(false);
  async function checkDorisNodeCount() {
    const res = await getComputeResourceList({
      workspaceId,
      engine: Engine.Doris,
      status: `${STATUS.RUNNING},${STATUS.DEPLOY},${STATUS.INVALID}`
    });
    if (res.success) {
      setIsDorisNodeCountLimit(res.result?.computes?.length >= 1);
    }
  }
  useEffect(() => {
    checkDorisNodeCount();
  }, [workspaceId]);

  function onRefresh() {
    checkDorisNodeCount();
    loadComputeList();
  }

  useImperativeHandle(ref, () => ({
    refresh: onRefresh
  }));

  // 跳转到数据目录页面
  const goCatalogPage = (catalog) => {
    navigate(`${urls.metaData}?workspaceId=${workspaceId}&catalog=${catalog}`);
  };

  const {renderOperate} = useTableOperate({
    onDelete(record: ComputeResourceItem) {
      Modal.confirm({
        title: '删除实例',
        content:
          '实例删除后不可恢复，关联任务的运行状态会受影响，关联任务需要重新指定计算实例。确定删除实例？',
        onOk: async () => {
          const res = await deleteComputeResource({
            workspaceId,
            computeId: record.computeId || ''
          });
          if (res.success) {
            toast.success({
              message: '删除成功',
              duration: 3
            });
            onRefresh();
          }
        }
      });
    }
  });

  const columns = useMemo(
    () =>
      [
        {
          title: '实例 ID',
          dataIndex: 'computeId',
          key: 'computeId',
          width: 150
        },
        {
          title: '实例名称',
          dataIndex: 'name',
          key: 'name',
          width: 200
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          width: 100,
          filterMultiple: true,
          filters: STATUS.toArray(),
          filteredValue: statusFilter,
          render: (status: string) => {
            const statusObj = STATUS.fromValue(status);
            return <span className={cx('compute-status', statusObj.className)}>{statusObj.text}</span>;
          }
        },
        {
          title: '节点规格',
          dataIndex: 'clusterType',
          key: 'clusterType',
          width: 120,
          render: (_, record: ComputeResourceItem) => `${record.clusterType}（${record.nodeCount} 个节点）`
        },
        {
          title: '实例版本',
          dataIndex: 'engine',
          key: 'engine',
          width: 120,
          render: () => 'Doris 3.0'
        },
        {
          title: '数据目录',
          dataIndex: 'catalog',
          key: 'catalog',
          width: 120,
          render: (_, record) =>
            record.status === 'RUNNING' ? (
              <TextEllipsis tooltip={record.name} width={120}>
                <Link onClick={() => goCatalogPage(record.name)}>{record.name}</Link>
              </TextEllipsis>
            ) : (
              '-'
            )
        },
        {
          title: '创建时间',
          dataIndex: 'createdAt',
          key: 'createdAt',
          sorter: true,
          width: 180
        },
        isPrivate
          ? null
          : {
              title: '付费方式',
              dataIndex: 'chargeType',
              key: 'chargeType',
              width: 120,
              render: (value: string) => {
                return PAY_TYPE.getTextFromValue(value);
              }
            },
        {
          title: '操作',
          key: 'operation',
          width: 150,
          render: (record: ComputeResourceItem) => {
            return renderOperate(record);
          }
        }
      ].filter(Boolean),
    [renderOperate, statusFilter, isPrivate]
  );
  const showBlankSpace = useMemo(
    () => dataSource.length === 0 && !statusFilter?.length,
    [dataSource.length, statusFilter?.length]
  );
  // 创建实例
  const onCreateClick = useCallback(() => {
    navigate(`${urls.computeCreateAnalysisInstance}?workspaceId=${workspaceId}`);
  }, [navigate, workspaceId]);
  const createButton = useMemo(() => {
    const button = (
      <Button
        className="ml-[8px]"
        type="primary"
        icon={<Plus1 className="w-4 h-4" />}
        onClick={onCreateClick}
        disabled={isDorisNodeCountLimit}
      >
        创建
      </Button>
    );
    return button;
  }, [onCreateClick, isDorisNodeCountLimit]);
  // 渲染空白状态
  const blankSpace = useMemo(() => {
    return (
      <div className={cx('blank-space', 'h-full')}>
        <div className={cx('blank-title')}>创建分析与AI搜索实例</div>
        <div className={cx('blank-desc')}>创建分析与AI搜索实例用于运行工作流任务</div>
        <Button
          className={cx('blank-btn')}
          type="primary"
          icon={<Plus1 className="w-4 h-4" />}
          onClick={onCreateClick}
        >
          立即创建
        </Button>
        <div className={cx('blank-img')}></div>
      </div>
    );
  }, [onCreateClick]);

  if (loading && dataSource.length === 0 && !statusFilter?.length) {
    return <Loading loading={loading} />;
  }

  return (
    <>
      {showBlankSpace ? (
        blankSpace
      ) : (
        <>
          <div className="flex justify-end mb-4">
            <RefreshButton onClick={onRefresh} />
            {createButton}
          </div>
          <Table className={cx('compute-table')} columns={columns} rowKey="computeId" {...tableProps} />
        </>
      )}
    </>
  );
};
const RefAnalysisInstanceList = forwardRef<RefreshableListRef>(AnalysisInstanceList);
export default RefAnalysisInstanceList;
