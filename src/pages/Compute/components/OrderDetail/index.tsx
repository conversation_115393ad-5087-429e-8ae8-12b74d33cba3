/**
 * 创建页 - 订单明细组件
 * 功能：
 * 1. 展示订单明细项
 * 2. 可选展示费用及订单明细
 * <AUTHOR>
 */
import React, {useEffect, useState, useRef} from 'react';
import {Checkbox, Button, toast, Popover, Table, Link} from 'acud';
import classNames from 'classnames/bind';
import styles from './index.module.less';
import {queryPrice} from '@api/billing';
import {PAY_TYPE} from '@pages/Compute/config';
import {getPayInfoUrl} from '@pages/Compute/utils';
import {useRegion} from '@hooks/useRegion';
import {isQasandbox} from '@pages/Compute/utils';
import IconSvg from '@components/IconSvg';
import useEnv from '@hooks/useEnv';

const cx = classNames.bind(styles);
interface SummaryItems {
  label: string;
  value: string | number;
}
interface OrderDetailProps {
  title?: string;
  items: SummaryItems[];
  showPrice?: boolean;
  price?: string | number;
  priceParams?: {
    chargingType: string;
    region: string;
    clusterType: string;
    nodeCnt: number;
    orderTime: string;
    subServiceType: string;
    workspaceId: string;
    tab: string;
    configs?: {label: string; value: string}[];
    detailTitle?: string;
    chargeItem?: string;
  };
  onSubmit?: () => void;
}
const OrderDetail: React.FC<OrderDetailProps> = ({
  title = 'DataBuilder 订单明细',
  items = [],
  showPrice = false,
  priceParams,
  onSubmit
}) => {
  const {isPrivate} = useEnv();
  const [serviceAgreement, setServiceAgreement] = useState(false);
  const [loading, setLoading] = useState(false);
  const {currentRegion} = useRegion();

  const handleSubmit = async () => {
    if (loading) {
      return;
    }
    if (!serviceAgreement) {
      toast.error({
        message: '请先阅读并同意服务协议信息',
        duration: 3
      });
      return;
    }
    setLoading(true);

    let res;
    try {
      res = await onSubmit();
    } catch (e) {
      console.error(e);
      setLoading(false);
      return;
    }
    // todo: 需要去掉
    if (!isQasandbox()) {
      setLoading(false);
      return;
    }
    const {orderId} = res.result;
    try {
      const {url} = await getPayInfoUrl({
        orderId,
        type: 'NEW',
        fromService: 'DATABUILDER',
        serviceType: 'EDAP',
        region: currentRegion?.id,
        customUrlParams: {
          workspaceId: priceParams?.workspaceId,
          tab: priceParams?.tab
        }
      });
      console.log(url);
      window.location.href = url;
    } catch (e) {
      console.error(e);
      setLoading(false);
    }
  };

  const [priceText, setPriceText] = useState('');
  useEffect(() => {
    // todo: 需要去掉
    if (!isQasandbox()) {
      return;
    }
    if (priceParams) {
      const {chargingType, region, clusterType, nodeCnt, orderTime, subServiceType} = priceParams;
      const [duration, timeUnit] = String(orderTime).split('-');
      if (!clusterType) {
        setPriceText('');
        setDataSource([]);
        return;
      }
      const params = {
        configs: [
          {
            serviceType: 'EDAP',
            count: 1,
            timeUnit: chargingType === PAY_TYPE.PREPAID ? String(timeUnit).toLocaleUpperCase() : 'MINUTE',
            duration: chargingType === PAY_TYPE.PREPAID ? Number(duration) : 1,
            region,
            scene: 'NEW',
            chargeItem: chargingType === PAY_TYPE.PREPAID ? 'Cpt2' : 'RunningTimeMinutes',
            flavor: [
              {
                name: clusterType,
                value: 1,
                scale: nodeCnt
              },
              {
                name: 'subServiceType',
                value: subServiceType
              }
            ]
          }
        ]
      };
      queryPrice(params).then((res) => {
        if (res.success) {
          const price = res.result?.[0]?.catalogPrice || '';
          const originPrice = res.result?.[0]?.realCatalogPrice || '';

          setPriceText(price);
          setDataSource([
            {
              chargeItem: priceParams?.chargeItem || '实例',
              flavor: `${clusterType}`,
              price,
              originPrice
            },
            {
              chargeItem: '总价',
              price,
              originPrice
            }
          ]);
        }
      });
    }
  }, [priceParams]);

  const columns = [
    {
      title: '计费项',
      dataIndex: 'chargeItem',
      key: 'chargeItem'
    },
    {
      title: '配置',
      dataIndex: 'flavor',
      key: 'flavor',
      width: 200,
      render: (text, record) => {
        if (record.chargeItem === '总价') {
          return null;
        }
        return (
          <>
            {priceParams?.configs?.map((item) => (
              <div className={cx('order-info-config-item')} key={item.label}>
                <span className={cx('item-label')}>{item.label}</span>
                <span className={cx('item-value')}>{item.value}</span>
              </div>
            ))}
          </>
        );
      }
    },
    {
      title: (
        <span>
          参考价格
          <Popover content="因小数保留位数有限，展示价格有轻微差异，实际请以账单为准">
            <IconSvg className="ml-[8px]" type="question" size={16} color="#84868C" fill="transparent" />
          </Popover>
        </span>
      ),
      dataIndex: 'price',
      key: 'price',
      width: 200,
      render: (text, record) => {
        const priceUnit = priceParams?.chargingType === PAY_TYPE.POSTPAID ? '/分钟' : '';
        return (
          <span>
            <span className={cx('price')}>
              ￥{text}
              {priceUnit}
            </span>
            {record.originPrice !== text && (
              <span className={cx('origin-price')}>
                ￥{record.originPrice}
                {priceUnit}
              </span>
            )}
          </span>
        );
      }
    }
  ];

  const [dataSource, setDataSource] = useState([]);

  return (
    <div className={cx('order-info')}>
      <div className={cx('order-info-title')}>{isPrivate ? '配置概览' : title}</div>
      <div className={cx('order-info-content')}>
        {items.map((item) => (
          <div className={cx('order-info-content-item')} key={item.label}>
            <span className={cx('order-info-content-item-label')}>{item.label}</span>
            <span className={cx('order-info-content-item-value')}>{item.value || '-'}</span>
          </div>
        ))}
      </div>
      {showPrice && (
        <>
          {isPrivate ? null : (
            <>
              <div className={cx('order-info-price')}>
                <div className={cx('order-info-price-title')}>费用</div>
                <div className={cx('order-info-price-value')}>
                  <span className={cx('symbol')}>￥</span>
                  <span className={cx('price-text')}>{priceText || '-'}</span>
                  {priceParams?.chargingType === PAY_TYPE.POSTPAID && (
                    <span className={cx('unit')}>/分钟</span>
                  )}
                  <Popover
                    placement="topRight"
                    content={
                      <div>
                        <div className={cx('order-info-price-detail-title')}>{priceParams?.detailTitle}</div>
                        <Table
                          className={cx('order-info-price-detail-table')}
                          pagination={false}
                          columns={columns}
                          dataSource={dataSource}
                        />
                      </div>
                    }
                  >
                    <span className={cx('order-info-price-detail')}>明细</span>
                  </Popover>
                </div>
              </div>
              <div className={cx('order-info-price-service-agreement')}>
                <Checkbox checked={serviceAgreement} onChange={(e) => setServiceAgreement(e.target.checked)}>
                  <>
                    我已阅读并同意
                    <Link
                      href="https://console.bce.baidu.com/iam/agreement-v2.html"
                      target="_blank"
                      rel="noreferrer"
                    >
                      《百度智能云线上订购协议》
                    </Link>
                  </>
                </Checkbox>
              </div>
            </>
          )}
          <Button className={cx('order-info-button')} type="primary" loading={loading} onClick={handleSubmit}>
            立即创建
          </Button>
        </>
      )}
    </div>
  );
};
export default OrderDetail;
