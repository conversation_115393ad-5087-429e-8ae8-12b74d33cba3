.compute-wrapper {
  padding: 0 8px 8px;
  height: 100%;
  box-sizing: border-box;

  .compute-content {
    overflow-y: auto;
    height: 100%;
    box-sizing: border-box;
    position: relative;
    background: #fff;
    border: 1px solid #d4d6d9;
    border-radius: 6px;
    padding: 16px;
  }

  .compute-title {
    font-size: 20px;
    color: #151b26;
    font-weight: bold;
    margin-bottom: 20px;

    .compute-title-icon {
      transform: rotate(180deg);
      cursor: pointer;
      margin-right: 12px;
    }
  }

  .form-wrapper {
    padding: 0 8px;

    .margin-bottom-40px {
      margin-bottom: 40px;
    }
  }
}
