import {<PERSON><PERSON><PERSON><PERSON>b, Button, Form, Input, Link, Select} from 'acud';
import React, {memo, ReactNode, useCallback, useContext, useEffect, useMemo, useState} from 'react';
import {WorkspaceContext} from '@pages/index';
import * as http from '@api/metaRequest';

import './index.less';
import IconSvg from '@components/IconSvg';
import ModalCreateName from '@components/MetaCreateModal/ModalCreateName';
import {MetaCnNameMap} from '@components/MetaCreateModal/constants';
import {RULE} from '@utils/regs';
import CreateVolumeModal from '@components/MetaCreateModal/CreateVolumeModal';
import CreateVolumeFolderModal from '@components/MetaCreateModal/CreateVolumeFolderModal';
import {CatalogType, EntityType, ValidateResult} from '@api/metaRequest';
import {EnumMetaType} from '../../api/metaRequest';
import {isArray, isNumber} from 'lodash';
import {useMemoizedFn} from 'ahooks';
import {FormInstance} from 'acud/lib/form';

const {Option} = Select;

interface FilePathSelectProps {
  // 是否展示路径选择器
  showPathSelect?: boolean;
  // 是否禁用输入框
  disabled?: boolean;
  // 选中文件卷（最后一个层级）后回调
  onChange?: (value: string) => void;
  // 元数据类型：volume、dataset、model
  metaType?: string;
  // 搜索类型
  searchType?: EntityType[];
  // 可浏览的目录列表数据：volume、dataset、model
  metaDirs: string[];
  // dataset、model 有版本
  versionName?: string;
  // 最低选中回填层级，后续层级默认可选中回填
  selectableLevel: PathLevel;
  // 最高展示层级，高于该层级，点击后不予展开，不填默认继续展开
  latestLevel?: PathLevel;

  name?: string | string[];
  label?: string;
  // 表单实例
  form?: FormInstance;
  // 关闭弹窗时间
  closeModalTime?: number;
}

interface PathItem {
  // 当时 item 的值
  value: string;
  // 完整路径（包含当前 item 的值）
  path: string[];
}

// 空状态类型
enum BlankType {
  // 搜索结果为空
  Search = 'search',
  // 列表为空
  List = 'list'
}

export enum PathLevel {
  // 所有目录，展示 catalog list
  All = -1,
  // catalog 下的目录，展示 schema
  Catalog = 0,
  // schema 下的目录，展示 volume
  Schema = 1,
  // schema 下的目录，展示 path
  Volume = 2,
  // 数据卷文件夹，最后一级选中目录
  Folder = 3
}

// 元数据类型
export enum MetaType {
  Volume = 'volume',
  Dataset = 'dataset',
  Table = 'table',
  Model = 'model'
}

const PREFIX = 'file-path-select';

const metaContent = {
  volume: {
    title: 'Volume',
    pathName: 'Volumes',
    invalidPathTip: '路径必须类似于 /Volumes/<catalog>/<schema>/<volume>/<path>',
    listFunc: (params: any) => {
      return http.getVolumeList({...params});
    },
    folderListFunc: (params: any) => {
      const {catalogName, schemaName, volumeName, path} = params;
      const reqPath = `/Volumes/${path}`;
      const fullName = `${catalogName}.${schemaName}.${volumeName}`;
      return http.getVolumeFileList(fullName, {
        path: reqPath
      });
    }
  },
  dataset: {
    title: 'Dataset',
    pathName: 'Datasets',
    invalidPathTip: '路径必须类似于 /Datasets/<catalog>/<schema>/<dataset>/<version>/<path>',
    listFunc: (params: any) => {
      return http.getDatasetOrModelList(http.EnumMetaType.DATASETS, {...params});
    },
    folderListFunc: (params: any) => {
      const {catalogName, schemaName, volumeName, path, versionName} = params;
      const reqPath = `/Datasets/${path}`;
      const fullName = `${catalogName}.${schemaName}.${volumeName}`;
      return http.getDatasetOrModelVersionFileList(EnumMetaType.DATASETS, fullName, versionName, {
        path: reqPath
      });
    }
  },
  model: {
    title: 'Model',
    pathName: 'Models',
    invalidPathTip: '路径必须类似于 /Models/<catalog>/<schema>/<model>/<version>/<path>',
    listFunc: (params: any) => {
      return http.getDatasetOrModelList(http.EnumMetaType.MODELS, {...params});
    },
    folderListFunc: (params: any) => {
      const {catalogName, schemaName, volumeName, path, versionName} = params;
      const reqPath = `/Models/${path}`;
      const fullName = `${catalogName}.${schemaName}.${volumeName}`;
      return http.getDatasetOrModelVersionFileList(EnumMetaType.MODELS, fullName, versionName, {
        path: reqPath
      });
    }
  },
  table: {
    title: 'Table',
    pathName: 'Tables',
    invalidPathTip: '路径必须类似于 <catalog>.<schema>.<table>',
    listFunc: (params: any) => {
      return http.getTableList({...params});
    },
    folderListFunc: (params: any) => {
      const {catalogName, schemaName, volumeName, path, versionName} = params;
      const reqPath = `/Models/${path}`;
      const fullName = `${catalogName}.${schemaName}.${volumeName}`;
      return http.getTableDetail(fullName);
    }
  }
};

const prefixCls = (base: string) => `${PREFIX}-${base}`;

const getCatalogList = async (workspaceId: string) => {
  const res = await http.getCatalogList({workspaceId});
  const catalogList = res.result.catalogs.filter((item) => item !== 'system');
  return catalogList || [];
};

const getSchemaList = async (workspaceId: string, catalogName: string) => {
  const schemaRes = await http.getSchemaList({
    workspaceId,
    catalogName
  });
  const schemaList = schemaRes.result.schemas;
  return schemaList || [];
};

const getVolumeList = async (
  workspaceId: string,
  catalogName: string,
  schemaName: string,
  metaDirs: string[]
) => {
  const funcArrs = metaDirs.map((item) => {
    const metaConf = metaContent[item];
    if (!metaConf) {
      return Promise.resolve();
    }
    return metaConf.listFunc({
      workspaceId,
      catalogName,
      schemaName
    });
  });

  const resList = await Promise.all(funcArrs);
  const lastResult = [];
  resList.forEach((res: any) => {
    const resResult = res && res.result;
    if (!resResult) {
      return;
    }
    const {volumes, datasets, models, tables} = resResult;
    const itemRes = volumes || datasets || models || tables || [];
    lastResult.push(...itemRes);
  });
  return lastResult;
};

const getVolumeFolderList = async (
  catalogName: string,
  schemaName: string,
  volumeName: string,
  versionName: string,
  metaDirs: string[],
  path: string
) => {
  const funcArrs = metaDirs.map((item) => {
    const metaConf = metaContent[item];
    if (!metaConf) {
      return Promise.resolve();
    }
    return metaConf.folderListFunc({
      catalogName,
      schemaName,
      volumeName,
      versionName,
      path
    });
  });
  const resList = await Promise.all(funcArrs);
  const lastResult = [];
  resList.forEach((res: any) => {
    if (!res) {
      return;
    }
    const itemRes = res.success ? res.result.files.filter((item) => !item.size).map((item) => item.name) : [];
    lastResult.push(...itemRes);
  });
  return lastResult;
};

const getSearchList = async (workspaceId, searchValue, searchType) => {
  // TODO 目前接口不支持 数据集 和 模型。联调需要关注。
  const res = await http.searchMeta(workspaceId, searchValue, searchType);
  return res.result.map((item) => ({
    value: item.name,
    path: item.parentName ? [...item.parentName.split('.'), item.name] : [item.name]
  }));
};

const createSchemaNameRules = (catalog) => {
  const ruleInfo = {
    rule: RULE.specialNameStartEn64,
    text: RULE.specialNameStartEn64Text
  };
  return [
    {
      validator: async (_, value) => {
        // 校验特殊字符和长度限制
        if (!ruleInfo.rule.test(value)) {
          return Promise.reject(new Error(ruleInfo.text));
        }
        // 异步校验Volume名称是否重复，复用查询接口 silent模式
        const res = await http.getSchemaDetail(`${catalog}.${value}`, true);
        if (res.success && res.result?.schema?.id) {
          return Promise.reject(new Error(`该${MetaCnNameMap['Schema']}名称已存在，请重新输入`));
        }
        return Promise.resolve();
      }
    }
  ];
};

const getLevel = (pathLength) => Math.min(PathLevel.Volume, pathLength);

const getCatalogDetail = async (catalog) => {
  const res = await http.getCatalogDetail(catalog);
  return res.result.catalog.type;
};

const getPathFromArr = (pathArr) => {
  if (!pathArr?.length) {
    return `/Volumes/`;
  }
  const [catalog, schema, volume, ...path] = pathArr;
  return `/Volumes/${catalog}/${schema}/${volume}/${path.join('')}`;
};

/**
 * 数据卷目录路径选择器
 */
const FilePathSelectFormItem: React.FC<FilePathSelectProps> = ({
  showPathSelect = true,
  disabled = false,
  metaType = MetaType.Volume,
  searchType = [EntityType.CATALOG, EntityType.SCHEMA, EntityType.VOLUME],
  metaDirs,
  versionName,
  selectableLevel,
  latestLevel,
  name = 'path',
  label,
  onChange = () => {},
  form,
  closeModalTime = 0
}) => {
  const {workspaceId} = useContext(WorkspaceContext);
  // 当前选中路径信息
  const [valueInfo, setValueInfo] = useState<PathItem>();
  // 展示列表
  const [showList, setShowList] = useState<PathItem[]>([]);
  // 空状态
  const [blankType, setBlankType] = useState<BlankType>();
  // 路径级别
  const [level, setLevel] = useState<PathLevel>(PathLevel.All);
  // 搜索项
  const [searchValue, setSearchValue] = useState<string>();
  // select 下拉框是否开启，通过浏览按钮控制
  const [dropdownOpen, setDropdownOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  // 添加弹窗可见
  const [modalVisible, setModalVisible] = useState<boolean>(false);

  const dealOnChangePath = useMemoizedFn((path) => {
    if (metaType === MetaType.Table) {
      // 替换全部 / 为 。 如果最后一个是点  也删除
      return path.replace(`/${metaContent[metaType].pathName}/`, '').replace(/\//g, '.').replace(/\.$/, '');
    }
    return path;
  });

  // 路径参数配置
  const pathConfig = useMemo(() => {
    const list = valueInfo?.path;
    return {
      // 展示 catalog 列表
      // 列表元素catalog
      // path ['catalog']
      [PathLevel.All]: {
        title: '所有目录',
        childLevel: PathLevel.Catalog,
        childIcon: 'meta-catalog',
        blankText: '无可选数据目录',
        func: () => getCatalogList(workspaceId)
      },
      // 展示 schema 列表
      // 列表元素 catalog/schema
      // path ['catalog', 'schema']
      [PathLevel.Catalog]: {
        title: '数据目录',
        childLevel: PathLevel.Schema,
        childIcon: 'meta-schema',
        blankText: '无可选数据模式',
        func: () => {
          const catalogName = list[PathLevel.Catalog];
          return getSchemaList(workspaceId, catalogName);
        }
      },
      // 展示 volume 列表
      // 列表元素 catalog/schema/volume
      // path ['catalog', 'schema', 'volume']
      // 可选中
      [PathLevel.Schema]: {
        title: '数据模式',
        childLevel: PathLevel.Volume,
        childIcon: 'meta-volume',
        blankText: `无可选${MetaCnNameMap[metaContent[metaType].title]}`,
        func: () => {
          const catalogName = list[PathLevel.Catalog];
          const schemaName = list[PathLevel.Schema];
          return getVolumeList(workspaceId, catalogName, schemaName, metaDirs);
        }
      },
      // 展示 folder 列表，volume 及 folder层级都适用该 level
      // 列表元素 catalog/scheme/volume/folder
      // path: ['catalog', 'schema', 'volume', 'folder']
      // 可选中
      [PathLevel.Volume]: {
        title: `${MetaCnNameMap[metaContent[metaType].title]}`,
        childLevel: PathLevel.Folder,
        childIcon: 'metadata-folder',
        blankText: '无可选文件夹',
        func: () => {
          const catalogName = list[PathLevel.Catalog];
          const schemaName = list[PathLevel.Schema];
          const volumeName = list[PathLevel.Volume];
          return getVolumeFolderList(
            catalogName,
            schemaName,
            volumeName,
            versionName,
            metaDirs,
            valueInfo?.path.join('/')
          );
        }
      }
    };
  }, [metaDirs, metaType, valueInfo?.path, versionName, workspaceId]);

  // 搜索回调
  const onSearch = useCallback((e) => {
    setSearchValue(e.target.value);
    // 回到所有目录下
    setLevel(PathLevel.All);
    setValueInfo(null);
  }, []);

  // 非搜索情况下，点击路径，跳转到下一层级
  const onNextPath = useCallback(
    (item: PathItem) => {
      setLevel(getLevel(level + 1));
      setValueInfo(item);
      setLoading(true);
    },
    [level]
  );

  // 搜索情况下，点击路径，跨层级跳转/选中
  const onSearchValueSelect = useCallback(
    (item: PathItem) => {
      setSearchValue('');
      const level = item.path.length;
      if (level > selectableLevel) {
        const temPath = dealOnChangePath(`/${metaContent[metaType].pathName}/${item.path.join('/')}`);
        onChange && onChange(temPath);
      }
      // 目前不能搜索文件夹，支持后这里考虑替换为 getLevel 方法
      setLevel(level - 1);
      setValueInfo(item);
    },
    [metaType, onChange, selectableLevel]
  );

  // 选中回调
  const onSelect = useCallback(
    (item) => {
      // 搜索
      if (searchValue) {
        onSearchValueSelect(item);
        return;
      }
      // 非搜索
      const path = item.path;

      // 数据卷/文件夹 可以选中回填
      if (path.length > selectableLevel) {
        const [catalog, schema, volume, ...path] = item.path;
        const schemaPath = schema ? `${schema}/` : '';
        const volumePath = volume ? `${volume}/` : '';
        const res = dealOnChangePath(
          `/${metaContent[metaType].pathName}/${catalog}/${schemaPath}${volumePath}${path.join('')}`
        );
        onChange && onChange(res);
        form.setFieldValue(name, res);
      }
      if (isNumber(latestLevel) && path.length - 2 >= latestLevel) {
        setDropdownOpen(false);
        return;
      }
      onNextPath(item);
    },
    [searchValue, latestLevel, onNextPath, selectableLevel, onSearchValueSelect, metaType, onChange, form]
  );

  // 点击新建弹窗回调
  const onAddClick = useCallback(() => {
    setModalVisible(true);
  }, []);

  // 无状态
  const notFoundContent = useMemo(() => {
    return (
      <div className={prefixCls('blank-icon')}>
        {blankType === BlankType.List ? (
          <>
            <div className={prefixCls('blank-icon-list')} />
            <div>
              {pathConfig?.[level]?.blankText}
              {/* 当前只支持 volume 创建文件夹，其他待定 */}
              {metaType === MetaType.Volume && (
                <>
                  ，<Link onClick={onAddClick}>立即创建</Link>
                </>
              )}
            </div>
          </>
        ) : (
          <>
            <div className={prefixCls('blank-icon-search')} />
            <div>搜索结果为空，请更换关键字重新搜索</div>
          </>
        )}
      </div>
    );
  }, [blankType, level, metaType, onAddClick, pathConfig]);

  const search = useMemo(() => {
    return (
      <Input
        placeholder="请输入关键字搜索数据目录、数据模式、数据卷"
        allowClear
        onChange={onSearch}
        value={searchValue}
      />
    );
  }, [onSearch, searchValue]);

  // 点击面包屑回调
  const onBreadcrumbClick = useCallback(
    (pathList, index: PathLevel) => {
      searchValue && setSearchValue('');
      setValueInfo({value: pathList[index], path: pathList.slice(0, index + 1)});
      setLevel(getLevel(index));
    },
    [searchValue]
  );

  const breadcrumb = useMemo(() => {
    const list = valueInfo?.path || [];
    return (
      <Breadcrumb>
        <Breadcrumb.Item>
          <Link onClick={() => onBreadcrumbClick([], PathLevel.All)}>所有目录</Link>
        </Breadcrumb.Item>
        {list.map((item, index) => (
          <Breadcrumb.Item key={item}>
            {/**最后一级无法点开，可以点到前面层级的目录 */}
            {index !== list.length - 1 ? (
              <Link onClick={() => onBreadcrumbClick(list, index)}>{item}</Link>
            ) : (
              item
            )}
          </Breadcrumb.Item>
        ))}
      </Breadcrumb>
    );
  }, [onBreadcrumbClick, valueInfo]);

  const dropdownRender = useCallback(
    (menu: ReactNode) => {
      return (
        <>
          <div className="mb-[12px] ml-[6px] mr-[6px]">{search}</div>
          <div className={`${prefixCls('breadcrumb')} mb-[12px]`}>
            {breadcrumb}
            {level === PathLevel.All || metaType !== MetaType.Volume ? null : (
              // TODO system 不能新建
              <IconSvg type="add" size={14} onClick={onAddClick} className="cursor-pointer" />
            )}
          </div>
          {menu}
        </>
      );
    },
    [breadcrumb, level, metaType, onAddClick, search]
  );

  const getList = useCallback(() => {
    pathConfig?.[level]
      ?.func()
      .then((res) => {
        const showList = res?.map((item) => ({
          value: item,
          path: valueInfo ? [...valueInfo.path, item] : [item]
        }));
        setShowList(showList);
      })
      .catch((err) => console.error(err))
      .finally(() => setLoading(false));
  }, [level, pathConfig, valueInfo]);

  // 请求列表
  useEffect(() => {
    if (searchValue) {
      getSearchList(workspaceId, searchValue, searchType)
        .then((res) => setShowList(res))
        .finally(() => setLoading(false));
    } else {
      getList();
    }
  }, [getList, searchValue, workspaceId, searchType]);

  const onDropdownHandler = useCallback(() => {
    setDropdownOpen((data) => !data);
  }, []);

  const onInputChange = useCallback(
    (e) => {
      const {value} = e.target;
      onChange && onChange(value);
    },
    [onChange]
  );

  useEffect(() => {
    if (!showList?.length) {
      searchValue ? setBlankType(BlankType.Search) : setBlankType(BlankType.List);
    } else {
      setBlankType(undefined);
    }
  }, [searchValue, showList?.length]);

  const onModalCancel = () => setModalVisible(false);

  useEffect(() => {
    return () => {
      // select 销毁的时候关闭 dropdown 组件
      setDropdownOpen(false);
    };
  }, []);

  const showCatalogDescription = useMemo(() => {
    if (modalVisible && level === PathLevel.Catalog) {
      getCatalogDetail(valueInfo?.value).then((res) => {
        return res !== CatalogType.DORIS;
      });
    } else {
      return true;
    }
  }, [level, modalVisible, valueInfo?.value]);
  const pathRules = [
    {required: true, message: '请输入路径'},
    {
      validator: async (_, value) => {
        let res = null;
        if (metaType === MetaType.Volume) {
          res = await http.validatePath(EnumMetaType.VOLUME, workspaceId, value);
        }
        if (metaType === MetaType.Dataset) {
          res = await http.validatePath(EnumMetaType.DATASETS, workspaceId, value);
        }
        if (metaType === MetaType.Model) {
          res = await http.validatePath(EnumMetaType.MODELS, workspaceId, value);
        }

        //  TODO 临时处理table 校验 等待有校验接口
        if (metaType === MetaType.Table) {
          try {
            if (latestLevel === PathLevel.Schema) {
              res = await http.getTableDetail(value);
            } else {
              res = await http.getSchemaDetail(value);
            }
          } catch (error) {
            return Promise.reject('路径错误');
          }
          if (res.success) {
            return Promise.resolve();
          }
          return Promise.reject('路径错误');
        }
        const validateResult = res.result.validateResult;
        if (res.success && validateResult !== ValidateResult.OK) {
          const msg = res.result.msg;

          const pathArr = value.replace(`/${metaContent[metaType].pathName}/`, '').split('/');
          const validateResultMsgMap = {
            [ValidateResult.INVALID]: metaContent[metaType].invalidPathTip,
            [ValidateResult.CATALOG_NOT_EXIST]: `数据目录 ${msg} 不存在`,
            [ValidateResult.SCHEMA_NOT_EXIST]: `数据模式 ${msg} 在 ${pathArr[0]} 下不存在`,
            [ValidateResult.VOLUME_NOT_EXIST]: `数据卷 ${msg} 在 ${pathArr[0]}.${pathArr[1]} 下不存在`,
            [ValidateResult.DIR_NOT_EXIST]: `目录 ${msg} 在 ${pathArr[0]}.${pathArr[1]}.${pathArr[2]} 下不存在`
          };
          return Promise.reject(new Error(validateResultMsgMap[validateResult]));
        }
        return Promise.resolve();
      }
    }
  ];
  const placeholder = useMemo(() => {
    if (metaType === MetaType.Table) {
      return 'catalog.schema.table';
    }
    return `/${metaContent[metaType].pathName}/catalog_name/schema_name/volume_name/directory_path`;
  }, [metaType]);

  useEffect(() => {
    if (closeModalTime) {
      setDropdownOpen(false);
    }
  }, [closeModalTime]);

  return (
    <div className={prefixCls('container')}>
      <Form.Item noStyle>
        <Form.Item rules={pathRules} name={name} label={label} className={prefixCls('form')}>
          <Input onInput={onInputChange} disabled={disabled} placeholder={placeholder} />
        </Form.Item>
        {showPathSelect ? (
          <>
            <Button className="ml-[8px]" onClick={onDropdownHandler}>
              {dropdownOpen ? '隐藏' : '浏览'}
            </Button>
            <Select
              className={prefixCls('select')}
              notFoundContent={notFoundContent}
              dropdownClassName={prefixCls('dropdown')}
              dropdownRender={dropdownRender}
              open={dropdownOpen}
              getPopupContainer={() => document.body}
              loading={loading}
            >
              {showList?.map((item) => (
                <Option value={item.value} key={item.path.join('/')}>
                  <div className={prefixCls('select-item')} onClick={() => onSelect(item)}>
                    <div className={prefixCls('select-item-value')}>
                      <IconSvg
                        type={pathConfig[getLevel(item.path.length - 2)]?.childIcon}
                        size={16}
                        color="#151B26"
                        className="mr-[8px]"
                      />
                      {item.value}
                      {/**搜索模式下，会展示整体路径 */}
                      {searchValue ? (
                        <div className={prefixCls('select-item-path')}>
                          {item.path.slice(0, item.path.length - 1).join('/')}
                        </div>
                      ) : null}
                    </div>

                    {item.path.length - 2 === latestLevel ? null : (
                      <IconSvg type="right" size={16} color="#333333" />
                    )}
                  </div>
                </Option>
              ))}
            </Select>
          </>
        ) : null}
      </Form.Item>

      {/** 创建schema弹窗 */}
      <ModalCreateName
        visible={modalVisible && level === PathLevel.Catalog}
        onCancel={onModalCancel}
        title="Schema"
        requestFun={http.createSchema}
        requestParams={{catalogName: valueInfo?.value}}
        successCallback={getList}
        nameRules={createSchemaNameRules(valueInfo?.value)}
        // TODO doris 类型还需要调接口判断
        showDescription={showCatalogDescription}
      />
      {/** 创建 Volume 弹窗 */}
      <CreateVolumeModal
        catalog={valueInfo?.path[0]}
        schema={valueInfo?.value}
        visible={modalVisible && level === PathLevel.Schema}
        handleCloseModal={onModalCancel}
        successCallback={getList}
      />
      {/** 创建 Volume 文件夹弹窗 */}
      <CreateVolumeFolderModal
        visible={modalVisible && metaType === MetaType.Volume && level >= PathLevel.Volume}
        fullName={`${valueInfo?.path[0]}.${valueInfo?.path[1]}.${valueInfo?.path[2]}`}
        onCancel={onModalCancel}
        successCallback={getList}
        path={`/${metaContent[metaType].pathName}/${valueInfo?.path.join('/')}`}
      />
    </div>
  );
};

export default memo(FilePathSelectFormItem);
